#!/usr/bin/env python3
"""
Historical Backup Processor for TNGD

This module provides backup functionality for historical data (specific date ranges)
using the existing table processor infrastructure. It can backup data from any
date range, including past months like March 2025.

Features:
- Backup specific date ranges
- Day-by-day processing for reliability
- Uses existing table processor infrastructure
- Comprehensive error handling and retry mechanism
- Progress tracking and validation
- Support for dry-run testing
- Configurable OSS path structure: Devo/{month}/week {n}/{date}/

Usage:
    python scripts/historical_backup_processor.py --start-date 2025-03-01 --end-date 2025-03-31 [options]

Options:
    --start-date DATE  Start date in YYYY-MM-DD format
    --end-date DATE    End date in YYYY-MM-DD format
    --dry-run          Validate only, no backup
    --verbose          Enable verbose logging
    --single-table     Test with single table only
"""

import os
import sys
import argparse
import logging
import time
import datetime
import json
from typing import Dict, Any, List, Optional
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import project modules
from core.backup_config import BackupConfig
from core.config_manager import ConfigManager
from core.unified_table_processor import UnifiedTableProcessor
from utils.minimal_logging import logger
from utils.notification import send_backup_summary_notification
from utils.disk_cleanup import cleanup_temp_files

class HistoricalBackupProcessor:
    """
    Historical backup processor that handles backups for specific date ranges
    using the existing table processor infrastructure.
    """

    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the historical backup processor.

        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager or ConfigManager()
        self.start_time = datetime.datetime.now()
        self.stats = {
            'total_days': 0,
            'completed_days': 0,
            'failed_days': 0,
            'total_tables': 0,
            'successful_tables': 0,
            'failed_tables': 0,
            'total_rows_backed_up': 0,
            'daily_results': {},
            'performance_metrics': {}
        }

    def load_table_list(self) -> List[str]:
        """
        Load table names from tabletest/tables.json.

        Returns:
            List of table names
        """
        try:
            tables = self.config_manager.get_tables_from_file()
            if not tables:
                logger.error("No tables found in tabletest/tables.json")
                return []

            logger.info(f"Loaded {len(tables)} tables from configuration")
            return tables

        except Exception as e:
            logger.error(f"Error loading table list: {str(e)}")
            return []

    def create_backup_config(self, target_date: datetime.date) -> BackupConfig:
        """
        Create backup configuration for a specific date.

        Args:
            target_date: Date to backup

        Returns:
            BackupConfig object
        """
        config = BackupConfig()

        # Set date-specific settings
        config.days = 1  # Always 1 day for daily processing
        config.specific_date = datetime.datetime.combine(target_date, datetime.time.min)
        config.chunk_size = self.config_manager.get('backup', 'default_chunk_size', 100000)
        config.max_retries = self.config_manager.get('backup', 'default_max_retries', 5)
        config.retry_delay = self.config_manager.get('backup', 'default_retry_delay', 5)
        config.timeout = self.config_manager.get('backup', 'default_timeout', 1800)
        config.max_concurrent_tables = 1  # Sequential processing
        config.validate = True

        return config

    def parse_date(self, date_str: str) -> datetime.date:
        """
        Parse date string in YYYY-MM-DD format.

        Args:
            date_str: Date string to parse

        Returns:
            datetime.date object

        Raises:
            ValueError: If date format is invalid
        """
        try:
            return datetime.datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError as e:
            raise ValueError(f"Invalid date format '{date_str}'. Expected YYYY-MM-DD format.") from e

    def generate_date_range(self, start_date: datetime.date, end_date: datetime.date) -> List[datetime.date]:
        """
        Generate list of dates between start and end date (inclusive).

        Args:
            start_date: Start date
            end_date: End date

        Returns:
            List of dates
        """
        if start_date > end_date:
            raise ValueError("Start date must be before or equal to end date")

        dates = []
        current_date = start_date
        while current_date <= end_date:
            dates.append(current_date)
            current_date += datetime.timedelta(days=1)

        return dates

    def run_historical_backup(self, args) -> Dict[str, Any]:
        """
        Execute the historical backup process.

        Args:
            args: Command line arguments

        Returns:
            Dictionary with backup results
        """
        logger.log_operation("HISTORICAL BACKUP PROCESS", "STARTED", 
                           f"Starting historical backup from {args.start_date} to {args.end_date}")

        # Parse dates
        try:
            start_date = self.parse_date(args.start_date)
            end_date = self.parse_date(args.end_date)
        except ValueError as e:
            logger.error(f"Date parsing error: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'stats': self.stats
            }

        # Generate date range
        try:
            dates_to_process = self.generate_date_range(start_date, end_date)
        except ValueError as e:
            logger.error(f"Date range error: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'stats': self.stats
            }

        # Load table list
        table_names = self.load_table_list()
        if not table_names:
            return {
                'status': 'error',
                'error': 'No tables to backup',
                'stats': self.stats
            }

        # Limit to single table for testing
        if args.single_table:
            table_names = table_names[:1]
            logger.info(f"Single table mode: using only {table_names[0]}")

        self.stats['total_days'] = len(dates_to_process)
        self.stats['total_tables'] = len(table_names) * len(dates_to_process)

        logger.info(f"Processing {len(dates_to_process)} days for {len(table_names)} tables")
        logger.info(f"Total table-day combinations: {self.stats['total_tables']}")

        if args.dry_run:
            return self._run_dry_run_validation(dates_to_process, table_names)
        else:
            return self._run_full_historical_backup(dates_to_process, table_names)

    def _run_dry_run_validation(self, dates_to_process: List[datetime.date],
                               table_names: List[str]) -> Dict[str, Any]:
        """
        Run dry-run validation for historical backup.

        Args:
            dates_to_process: List of dates to validate
            table_names: List of table names

        Returns:
            Validation results
        """
        logger.info("Running dry-run validation for historical backup")

        validation_stats = {
            'total_days': len(dates_to_process),
            'total_tables': len(table_names),
            'total_combinations': len(dates_to_process) * len(table_names),
            'date_range': f"{dates_to_process[0]} to {dates_to_process[-1]}"
        }

        # Test configuration for first date
        if dates_to_process:
            test_date = dates_to_process[0]
            logger.info(f"Testing configuration for date: {test_date}")

            try:
                config = self.create_backup_config(test_date)
                logger.info(f"Configuration created successfully for {test_date}")
                logger.info(f"Chunk size: {config.chunk_size}")
                logger.info(f"Timeout: {config.timeout}")
                logger.info(f"Max retries: {config.max_retries}")
            except Exception as e:
                logger.error(f"Configuration test failed: {str(e)}")
                validation_stats['config_error'] = str(e)

        logger.info(f"Validation complete: {validation_stats}")
        return {
            'status': 'success',
            'mode': 'dry_run',
            'validation_stats': validation_stats,
            'stats': self.stats
        }

    def _run_full_historical_backup(self, dates_to_process: List[datetime.date],
                                   table_names: List[str]) -> Dict[str, Any]:
        """
        Run full historical backup processing.

        Args:
            dates_to_process: List of dates to process
            table_names: List of table names

        Returns:
            Backup results
        """
        logger.info("Starting full historical backup process")

        for day_date in dates_to_process:
            day_str = day_date.strftime('%Y-%m-%d')
            logger.info(f"Processing day: {day_str}")

            try:
                # Create backup configuration for this specific date
                config = self.create_backup_config(day_date)

                # Use unified table processor for this day
                processor = UnifiedTableProcessor(config, strategy='basic')

                # Process each table for this date
                day_successful = 0
                day_failed = 0
                day_rows = 0

                for table_name in table_names:
                    try:
                        logger.info(f"Processing table {table_name} for {day_str}")
                        result = processor.backup_table(table_name, mode='historical')

                        if result.get('status') == 'success':
                            day_successful += 1
                            day_rows += result.get('total_rows', 0)
                        else:
                            day_failed += 1

                    except Exception as e:
                        logger.error(f"Table {table_name} failed for {day_str}: {str(e)}")
                        day_failed += 1

                # Update stats
                self.stats['completed_days'] += 1
                self.stats['successful_tables'] += day_successful
                self.stats['failed_tables'] += day_failed
                self.stats['total_rows_backed_up'] += day_rows
                self.stats['daily_results'][day_str] = {
                    'successful_tables': day_successful,
                    'failed_tables': day_failed,
                    'total_rows': day_rows
                }

                logger.info(f"Day {day_str} completed: {day_successful} successful, {day_failed} failed")

            except Exception as e:
                logger.error(f"Day {day_str} failed: {str(e)}")
                self.stats['failed_days'] += 1

        return {
            'status': 'success' if self.stats['failed_days'] == 0 else 'partial',
            'mode': 'full_historical_backup',
            'stats': self.stats
        }

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Historical Backup Processor for TNGD',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    parser.add_argument('--start-date', required=True,
                       help='Start date in YYYY-MM-DD format')
    parser.add_argument('--end-date', required=True,
                       help='End date in YYYY-MM-DD format')
    parser.add_argument('--dry-run', action='store_true',
                       help='Validate only, do not backup')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--single-table', action='store_true',
                       help='Test with single table only')

    return parser.parse_args()

def main():
    """Main function for historical backup processor."""
    args = parse_arguments()

    try:
        # Log startup
        logger.log_operation("HISTORICAL BACKUP PROCESSOR", "STARTED",
                           f"Started at: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # Create and run historical backup processor
        processor = HistoricalBackupProcessor()
        backup_result = processor.run_historical_backup(args)

        # Return appropriate exit code
        if backup_result['status'] == 'error':
            logger.error("Historical backup failed")
            return 1
        elif backup_result['status'] == 'partial':
            logger.warning("Historical backup completed with some failures")
            return 1
        else:
            logger.info("Historical backup completed successfully")
            return 0

    except KeyboardInterrupt:
        logger.warning("Historical backup interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error in historical backup: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
