#!/usr/bin/env python3
"""
Unified Exception Hierarchy for TNGD Backup System

This module provides a standardized exception hierarchy to replace the inconsistent
error handling patterns across the project. All modules should use these exceptions
for consistent error handling and logging.

Features:
- Hierarchical exception structure
- Standardized error context and details
- Security-aware error messages (no sensitive data leakage)
- Integration with logging system
- Error recovery hints
"""

import logging
import traceback
from typing import Dict, Any, Optional, List
from functools import wraps

# Configure logging
logger = logging.getLogger(__name__)

class BackupSystemError(Exception):
    """
    Base exception for all backup system errors.
    
    This is the root of the exception hierarchy and should be used
    as the base class for all custom exceptions in the system.
    """
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None, 
                 error_code: Optional[str] = None, recovery_hint: Optional[str] = None):
        """
        Initialize backup system error.
        
        Args:
            message: Human-readable error message
            details: Additional error details (will be sanitized)
            error_code: Unique error code for programmatic handling
            recovery_hint: Suggestion for error recovery
        """
        super().__init__(message)
        self.message = message
        self.details = self._sanitize_details(details or {})
        self.error_code = error_code
        self.recovery_hint = recovery_hint
        self.timestamp = None
        
    def _sanitize_details(self, details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize error details to prevent sensitive data leakage.
        
        Args:
            details: Raw error details
            
        Returns:
            Sanitized error details
        """
        sanitized = {}
        sensitive_keys = {'password', 'secret', 'key', 'token', 'credential'}
        
        for key, value in details.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                sanitized[key] = '[REDACTED]'
            else:
                sanitized[key] = str(value)[:1000]  # Limit length
        
        return sanitized
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert exception to dictionary for logging/serialization.
        
        Returns:
            Dictionary representation of the exception
        """
        return {
            'error_type': self.__class__.__name__,
            'message': self.message,
            'details': self.details,
            'error_code': self.error_code,
            'recovery_hint': self.recovery_hint,
            'timestamp': self.timestamp
        }


class ConfigurationError(BackupSystemError):
    """
    Configuration-related errors.
    
    Raised when there are issues with configuration files, environment variables,
    or invalid configuration values.
    """
    
    def __init__(self, message: str, config_key: Optional[str] = None, 
                 config_file: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.config_key = config_key
        self.config_file = config_file
        
        if not self.recovery_hint:
            self.recovery_hint = "Check configuration file and environment variables"


class StorageError(BackupSystemError):
    """
    Storage operation errors.
    
    Raised when there are issues with file operations, OSS uploads/downloads,
    or storage-related functionality.
    """
    
    def __init__(self, message: str, operation: Optional[str] = None, 
                 file_path: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.operation = operation
        self.file_path = file_path
        
        if not self.recovery_hint:
            self.recovery_hint = "Check file permissions and disk space"


class ValidationError(BackupSystemError):
    """
    Data validation errors.
    
    Raised when data validation fails, including input validation,
    backup integrity checks, or data consistency issues.
    """
    
    def __init__(self, message: str, validation_type: Optional[str] = None, 
                 expected_value: Optional[str] = None, actual_value: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.validation_type = validation_type
        self.expected_value = expected_value
        self.actual_value = actual_value
        
        if not self.recovery_hint:
            self.recovery_hint = "Verify input data and retry operation"


class NetworkError(BackupSystemError):
    """
    Network connectivity errors.
    
    Raised when there are network-related issues, including API timeouts,
    connection failures, or service unavailability.
    """
    
    def __init__(self, message: str, endpoint: Optional[str] = None, 
                 status_code: Optional[int] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.endpoint = endpoint
        self.status_code = status_code
        
        if not self.recovery_hint:
            self.recovery_hint = "Check network connectivity and retry with exponential backoff"


class ApiError(BackupSystemError):
    """
    API-related errors.
    
    Raised when there are issues with external API calls, including
    authentication failures, rate limiting, or invalid responses.
    """
    
    def __init__(self, message: str, api_name: Optional[str] = None, 
                 request_id: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.api_name = api_name
        self.request_id = request_id
        
        if not self.recovery_hint:
            self.recovery_hint = "Check API credentials and rate limits"


class CompressionError(BackupSystemError):
    """
    Compression operation errors.
    
    Raised when there are issues with file compression or decompression operations.
    """
    
    def __init__(self, message: str, algorithm: Optional[str] = None, 
                 source_path: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.algorithm = algorithm
        self.source_path = source_path
        
        if not self.recovery_hint:
            self.recovery_hint = "Try different compression algorithm or check source files"


class ChecksumError(BackupSystemError):
    """
    Checksum calculation or verification errors.
    
    Raised when there are issues with file integrity verification or checksum calculations.
    """
    
    def __init__(self, message: str, algorithm: Optional[str] = None, 
                 expected_checksum: Optional[str] = None, actual_checksum: Optional[str] = None, **kwargs):
        super().__init__(message, **kwargs)
        self.algorithm = algorithm
        self.expected_checksum = expected_checksum
        self.actual_checksum = actual_checksum
        
        if not self.recovery_hint:
            self.recovery_hint = "Verify file integrity and recalculate checksums"


# Error handling decorator
def handle_errors(error_context: str, reraise: bool = True, 
                 default_return: Any = None, log_level: int = logging.ERROR):
    """
    Decorator for standardized error handling.
    
    Args:
        error_context: Context description for error messages
        reraise: Whether to reraise the exception after handling
        default_return: Default return value if not reraising
        log_level: Logging level for error messages
        
    Returns:
        Decorated function with error handling
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except BackupSystemError as e:
                # Already a system error, just log and reraise/return
                logger.log(log_level, f"Error in {error_context}: {e.message}")
                if e.details:
                    logger.debug(f"Error details: {e.details}")
                
                if reraise:
                    raise
                return default_return
                
            except Exception as e:
                # Convert to system error
                error_msg = f"{error_context} failed: {str(e)}"
                logger.log(log_level, error_msg)
                logger.debug(f"Traceback: {traceback.format_exc()}")
                
                if reraise:
                    raise BackupSystemError(error_msg, details={'original_error': str(e)}) from e
                return default_return
                
        return wrapper
    return decorator


def log_error_with_context(error: Exception, context: str, 
                          additional_details: Optional[Dict[str, Any]] = None):
    """
    Log error with standardized format and context.
    
    Args:
        error: Exception to log
        context: Context where error occurred
        additional_details: Additional details to include
    """
    if isinstance(error, BackupSystemError):
        logger.error(f"[{context}] {error.message}")
        if error.details:
            logger.debug(f"[{context}] Error details: {error.details}")
        if error.recovery_hint:
            logger.info(f"[{context}] Recovery hint: {error.recovery_hint}")
    else:
        logger.error(f"[{context}] {str(error)}")
        logger.debug(f"[{context}] Traceback: {traceback.format_exc()}")
    
    if additional_details:
        logger.debug(f"[{context}] Additional details: {additional_details}")


def create_error_summary(errors: List[Exception]) -> Dict[str, Any]:
    """
    Create summary of multiple errors for reporting.
    
    Args:
        errors: List of exceptions
        
    Returns:
        Error summary dictionary
    """
    summary = {
        'total_errors': len(errors),
        'error_types': {},
        'critical_errors': [],
        'recovery_hints': set()
    }
    
    for error in errors:
        error_type = type(error).__name__
        summary['error_types'][error_type] = summary['error_types'].get(error_type, 0) + 1
        
        if isinstance(error, BackupSystemError):
            if error.recovery_hint:
                summary['recovery_hints'].add(error.recovery_hint)
            
            # Consider certain errors as critical
            if isinstance(error, (ConfigurationError, StorageError)):
                summary['critical_errors'].append(error.to_dict())
    
    summary['recovery_hints'] = list(summary['recovery_hints'])
    return summary
