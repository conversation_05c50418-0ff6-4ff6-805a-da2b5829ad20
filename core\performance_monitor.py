#!/usr/bin/env python3
"""
Performance Monitoring Module

This module provides functionality for monitoring and reporting on the performance
of the backup process, including resource usage, query performance, and storage operations.
"""

import os
import sys
import time
import json
import logging
import datetime
import threading
import statistics
from typing import Dict, Any, List, Optional, Tuple, Union
from threading import Lock

import psutil

from core.config_manager import ConfigManager

# Configure logging
logger = logging.getLogger(__name__)

class PerformanceMetric:
    """
    Class to track a single performance metric over time.
    Provides methods to record values and calculate statistics.
    """

    def __init__(self, name: str, unit: str = "", description: str = ""):
        """
        Initialize a performance metric.

        Args:
            name: Name of the metric
            unit: Unit of measurement (e.g., "seconds", "MB", "%")
            description: Description of what the metric measures
        """
        self.name = name
        self.unit = unit
        self.description = description
        self.values = []
        self.start_time = time.time()
        self.last_value = None
        self.min_value = None
        self.max_value = None
        self.total_value = 0
        self.count = 0

    def record(self, value: float) -> None:
        """
        Record a new value for this metric.

        Args:
            value: The value to record
        """
        self.values.append(value)
        self.last_value = value
        self.total_value += value
        self.count += 1

        # Update min and max values
        if self.min_value is None or value < self.min_value:
            self.min_value = value
        if self.max_value is None or value > self.max_value:
            self.max_value = value

    def get_statistics(self) -> Dict[str, Any]:
        """
        Calculate statistics for this metric.

        Returns:
            Dictionary with statistics
        """
        if not self.values:
            return {
                "name": self.name,
                "unit": self.unit,
                "description": self.description,
                "count": 0,
                "min": None,
                "max": None,
                "mean": None,
                "median": None,
                "std_dev": None,
                "last": None,
                "total": None
            }

        return {
            "name": self.name,
            "unit": self.unit,
            "description": self.description,
            "count": self.count,
            "min": self.min_value,
            "max": self.max_value,
            "mean": statistics.mean(self.values) if self.values else None,
            "median": statistics.median(self.values) if self.values else None,
            "std_dev": statistics.stdev(self.values) if len(self.values) > 1 else 0,
            "last": self.last_value,
            "total": self.total_value
        }

    def reset(self) -> None:
        """Reset the metric to its initial state."""
        self.values = []
        self.start_time = time.time()
        self.last_value = None
        self.min_value = None
        self.max_value = None
        self.total_value = 0
        self.count = 0


class PerformanceMonitor:
    """
    Performance monitoring system for the backup process.
    Tracks resource usage, query performance, and storage operations.
    """

    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the performance monitor.

        Args:
            config_manager: Optional ConfigManager instance
        """
        self.config_manager = config_manager or ConfigManager()
        self.enabled = self.config_manager.get('performance_monitoring', 'enabled', True)

        if not self.enabled:
            logger.info("Performance monitoring is disabled")
            return

        # Initialize configuration
        self.metrics_interval = self.config_manager.get('performance_monitoring', 'metrics_collection_interval', 5)
        self.detailed_metrics = self.config_manager.get('performance_monitoring', 'detailed_metrics', True)
        self.resource_monitoring = self.config_manager.get('performance_monitoring', 'resource_monitoring', True)
        self.query_tracking = self.config_manager.get('performance_monitoring', 'query_performance_tracking', True)
        self.storage_tracking = self.config_manager.get('performance_monitoring', 'storage_performance_tracking', True)
        self.thread_tracking = self.config_manager.get('performance_monitoring', 'thread_performance_tracking', True)
        self.save_reports = self.config_manager.get('performance_monitoring', 'save_performance_reports', True)

        # Initialize alert thresholds
        self.alert_thresholds = self.config_manager.get('performance_monitoring', 'alert_thresholds', {})

        # Initialize performance profiles
        self.performance_profiles = self.config_manager.get('performance_monitoring', 'performance_profiles', {})

        # Initialize metrics storage
        self.metrics: Dict[str, PerformanceMetric] = {}
        self.table_metrics: Dict[str, Dict[str, PerformanceMetric]] = {}
        self.resource_metrics: Dict[str, PerformanceMetric] = {}
        self.query_metrics: Dict[str, PerformanceMetric] = {}
        self.storage_metrics: Dict[str, PerformanceMetric] = {}
        self.thread_metrics: Dict[str, PerformanceMetric] = {}

        # Initialize locks
        self.metrics_lock = Lock()
        self.resource_lock = Lock()

        # Initialize monitoring thread
        self.monitoring_active = False
        self.monitoring_thread = None

        # Initialize start time
        self.start_time = time.time()

        # Initialize resource monitoring
        if self.resource_monitoring:
            self._init_resource_metrics()

        logger.info(f"Performance monitoring initialized with interval {self.metrics_interval}s")

    def _init_resource_metrics(self) -> None:
        """Initialize resource monitoring metrics."""
        with self.resource_lock:
            # CPU metrics
            self.resource_metrics["cpu_percent"] = PerformanceMetric(
                "cpu_percent", "%", "CPU usage percentage"
            )
            self.resource_metrics["cpu_count"] = PerformanceMetric(
                "cpu_count", "cores", "Number of CPU cores"
            )

            # Memory metrics
            self.resource_metrics["memory_percent"] = PerformanceMetric(
                "memory_percent", "%", "Memory usage percentage"
            )
            self.resource_metrics["memory_available"] = PerformanceMetric(
                "memory_available", "MB", "Available memory in MB"
            )
            self.resource_metrics["memory_used"] = PerformanceMetric(
                "memory_used", "MB", "Used memory in MB"
            )

            # Disk metrics
            self.resource_metrics["disk_percent"] = PerformanceMetric(
                "disk_percent", "%", "Disk usage percentage"
            )
            self.resource_metrics["disk_free"] = PerformanceMetric(
                "disk_free", "MB", "Free disk space in MB"
            )

            # Process metrics
            self.resource_metrics["process_cpu_percent"] = PerformanceMetric(
                "process_cpu_percent", "%", "Process CPU usage percentage"
            )
            self.resource_metrics["process_memory_percent"] = PerformanceMetric(
                "process_memory_percent", "%", "Process memory usage percentage"
            )
            self.resource_metrics["process_threads"] = PerformanceMetric(
                "process_threads", "count", "Number of threads in the process"
            )

    def start_monitoring(self) -> None:
        """Start the performance monitoring thread."""
        if not self.enabled:
            return

        if self.monitoring_active:
            logger.warning("Performance monitoring is already active")
            return

        def monitor_resources():
            """Background thread to monitor system resources."""
            logger.info("Resource monitoring thread started")

            while self.monitoring_active:
                try:
                    self._collect_resource_metrics()
                    time.sleep(self.metrics_interval)
                except Exception as e:
                    logger.error(f"Error in resource monitoring thread: {str(e)}")
                    time.sleep(self.metrics_interval)

        # Start the monitoring thread
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=monitor_resources, daemon=True)
        self.monitoring_thread.start()

        logger.info("Performance monitoring started")

    def stop_monitoring(self) -> None:
        """Stop the performance monitoring thread."""
        if not self.enabled or not self.monitoring_active:
            return

        self.monitoring_active = False

        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=5)

        logger.info("Performance monitoring stopped")

    def _collect_resource_metrics(self) -> None:
        """Collect system resource metrics."""
        if not self.enabled or not self.resource_monitoring:
            return

        try:
            # Get CPU metrics
            cpu_percent = psutil.cpu_percent(interval=0.1)
            cpu_count = psutil.cpu_count()

            # Get memory metrics
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available_mb = memory.available / (1024 * 1024)
            memory_used_mb = memory.used / (1024 * 1024)

            # Get disk metrics
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_free_mb = disk.free / (1024 * 1024)

            # Get process metrics
            process = psutil.Process()
            process_cpu_percent = process.cpu_percent(interval=0.1)
            process_memory_percent = process.memory_percent()
            process_threads = len(process.threads())

            # Record metrics
            with self.resource_lock:
                self.resource_metrics["cpu_percent"].record(cpu_percent)
                self.resource_metrics["cpu_count"].record(cpu_count)
                self.resource_metrics["memory_percent"].record(memory_percent)
                self.resource_metrics["memory_available"].record(memory_available_mb)
                self.resource_metrics["memory_used"].record(memory_used_mb)
                self.resource_metrics["disk_percent"].record(disk_percent)
                self.resource_metrics["disk_free"].record(disk_free_mb)
                self.resource_metrics["process_cpu_percent"].record(process_cpu_percent)
                self.resource_metrics["process_memory_percent"].record(process_memory_percent)
                self.resource_metrics["process_threads"].record(process_threads)

            # Check for alerts
            self._check_resource_alerts(cpu_percent, memory_percent, disk_percent)

        except Exception as e:
            logger.error(f"Error collecting resource metrics: {str(e)}")

    def _check_resource_alerts(self, cpu_percent: float, memory_percent: float, disk_percent: float) -> None:
        """
        Check resource metrics against alert thresholds.

        Args:
            cpu_percent: Current CPU usage percentage
            memory_percent: Current memory usage percentage
            disk_percent: Current disk usage percentage
        """
        # Get alert thresholds
        cpu_threshold = self.alert_thresholds.get("cpu_percent", 90)
        memory_threshold = self.alert_thresholds.get("memory_percent", 85)
        disk_threshold = self.alert_thresholds.get("disk_percent", 90)

        # Check CPU usage
        if cpu_percent >= cpu_threshold:
            logger.warning(f"ALERT: High CPU usage: {cpu_percent:.1f}% (threshold: {cpu_threshold}%)")

        # Check memory usage
        if memory_percent >= memory_threshold:
            logger.warning(f"ALERT: High memory usage: {memory_percent:.1f}% (threshold: {memory_threshold}%)")

        # Check disk usage
        if disk_percent >= disk_threshold:
            logger.warning(f"ALERT: High disk usage: {disk_percent:.1f}% (threshold: {disk_threshold}%)")

    def record_query_time(self, table_name: str, query_time: float, rows: int) -> None:
        """
        Record query execution time for a table.

        Args:
            table_name: Name of the table
            query_time: Query execution time in seconds
            rows: Number of rows returned
        """
        if not self.enabled or not self.query_tracking:
            return

        with self.metrics_lock:
            # Create table metrics dictionary if it doesn't exist
            if table_name not in self.table_metrics:
                self.table_metrics[table_name] = {}

            # Create query metrics for this table if they don't exist
            if "query_time" not in self.table_metrics[table_name]:
                self.table_metrics[table_name]["query_time"] = PerformanceMetric(
                    "query_time", "seconds", f"Query execution time for {table_name}"
                )

            if "rows_per_second" not in self.table_metrics[table_name]:
                self.table_metrics[table_name]["rows_per_second"] = PerformanceMetric(
                    "rows_per_second", "rows/s", f"Query processing rate for {table_name}"
                )

            # Record metrics
            self.table_metrics[table_name]["query_time"].record(query_time)

            # Calculate and record rows per second
            if query_time > 0:
                rows_per_second = rows / query_time
                self.table_metrics[table_name]["rows_per_second"].record(rows_per_second)

            # Check for alerts
            query_threshold = self.alert_thresholds.get("query_time_seconds", 300)
            if query_time >= query_threshold:
                logger.warning(f"ALERT: Slow query for {table_name}: {query_time:.1f}s (threshold: {query_threshold}s)")

            # Check against performance profile
            self._check_performance_profile(table_name, "query", query_time, rows)

    def record_processing_time(self, table_name: str, processing_time: float, rows: int) -> None:
        """
        Record data processing time for a table.

        Args:
            table_name: Name of the table
            processing_time: Processing time in seconds
            rows: Number of rows processed
        """
        if not self.enabled:
            return

        with self.metrics_lock:
            # Create table metrics dictionary if it doesn't exist
            if table_name not in self.table_metrics:
                self.table_metrics[table_name] = {}

            # Create processing metrics for this table if they don't exist
            if "processing_time" not in self.table_metrics[table_name]:
                self.table_metrics[table_name]["processing_time"] = PerformanceMetric(
                    "processing_time", "seconds", f"Data processing time for {table_name}"
                )

            if "processing_rate" not in self.table_metrics[table_name]:
                self.table_metrics[table_name]["processing_rate"] = PerformanceMetric(
                    "processing_rate", "rows/s", f"Data processing rate for {table_name}"
                )

            # Record metrics
            self.table_metrics[table_name]["processing_time"].record(processing_time)

            # Calculate and record processing rate
            if processing_time > 0:
                processing_rate = rows / processing_time
                self.table_metrics[table_name]["processing_rate"].record(processing_rate)

            # Check against performance profile
            self._check_performance_profile(table_name, "processing", processing_time, rows)

    def record_upload_time(self, table_name: str, upload_time: float, size_mb: float) -> None:
        """
        Record data upload time for a table.

        Args:
            table_name: Name of the table
            upload_time: Upload time in seconds
            size_mb: Size of the uploaded data in MB
        """
        if not self.enabled or not self.storage_tracking:
            return

        with self.metrics_lock:
            # Create table metrics dictionary if it doesn't exist
            if table_name not in self.table_metrics:
                self.table_metrics[table_name] = {}

            # Create upload metrics for this table if they don't exist
            if "upload_time" not in self.table_metrics[table_name]:
                self.table_metrics[table_name]["upload_time"] = PerformanceMetric(
                    "upload_time", "seconds", f"Data upload time for {table_name}"
                )

            if "upload_rate" not in self.table_metrics[table_name]:
                self.table_metrics[table_name]["upload_rate"] = PerformanceMetric(
                    "upload_rate", "MB/s", f"Data upload rate for {table_name}"
                )

            # Record metrics
            self.table_metrics[table_name]["upload_time"].record(upload_time)

            # Calculate and record upload rate
            if upload_time > 0 and size_mb > 0:
                upload_rate = size_mb / upload_time
                self.table_metrics[table_name]["upload_rate"].record(upload_rate)

            # Check for alerts
            upload_threshold = self.alert_thresholds.get("upload_time_seconds", 300)
            if upload_time >= upload_threshold:
                logger.warning(f"ALERT: Slow upload for {table_name}: {upload_time:.1f}s (threshold: {upload_threshold}s)")

            # Check against performance profile
            self._check_performance_profile(table_name, "upload", upload_time, size_mb)

    def record_storage_time(self, table_name: str, storage_time: float, size_mb: float) -> None:
        """
        Record data storage time for a table.

        Args:
            table_name: Name of the table
            storage_time: Storage time in seconds
            size_mb: Size of the stored data in MB
        """
        if not self.enabled or not self.storage_tracking:
            return

        with self.metrics_lock:
            # Create table metrics dictionary if it doesn't exist
            if table_name not in self.table_metrics:
                self.table_metrics[table_name] = {}

            # Create storage metrics for this table if they don't exist
            if "storage_time" not in self.table_metrics[table_name]:
                self.table_metrics[table_name]["storage_time"] = PerformanceMetric(
                    "storage_time", "seconds", f"Data storage time for {table_name}"
                )

            if "storage_rate" not in self.table_metrics[table_name]:
                self.table_metrics[table_name]["storage_rate"] = PerformanceMetric(
                    "storage_rate", "MB/s", f"Data storage rate for {table_name}"
                )

            # Record metrics
            self.table_metrics[table_name]["storage_time"].record(storage_time)

            # Calculate and record storage rate
            if storage_time > 0 and size_mb > 0:
                storage_rate = size_mb / storage_time
                self.table_metrics[table_name]["storage_rate"].record(storage_rate)

            # Check for alerts
            storage_threshold = self.alert_thresholds.get("storage_time_seconds", 300)
            if storage_time >= storage_threshold:
                logger.warning(f"ALERT: Slow storage for {table_name}: {storage_time:.1f}s (threshold: {storage_threshold}s)")

            # Check against performance profile
            self._check_performance_profile(table_name, "storage", storage_time, size_mb)

    def _check_performance_profile(self, table_name: str, operation_type: str,
                                 time_taken: float, size: float) -> None:
        """
        Check performance against defined profiles.

        Args:
            table_name: Name of the table
            operation_type: Type of operation (query, processing, upload)
            time_taken: Time taken for the operation in seconds
            size: Size of the operation (rows or MB)
        """
        # Determine the appropriate profile based on size
        profile_name = None
        for name, profile in self.performance_profiles.items():
            max_rows = profile.get("max_rows", 0)
            if size <= max_rows:
                profile_name = name
                break

        if not profile_name:
            # Use the largest profile if no match
            profile_name = next(reversed(self.performance_profiles.keys()), None)

        if not profile_name:
            return  # No profiles defined

        # Get the expected time for this operation type
        profile = self.performance_profiles[profile_name]
        expected_time_key = f"expected_{operation_type}_time_seconds"
        expected_time = profile.get(expected_time_key)

        if expected_time is None:
            return  # No expected time defined for this operation

        # Calculate the ratio of actual to expected time
        ratio = time_taken / expected_time

        # Log if the operation took significantly longer than expected
        if ratio > 2.0:
            logger.warning(f"Performance warning: {operation_type} for {table_name} took {time_taken:.1f}s "
                         f"({ratio:.1f}x longer than expected for {profile_name} profile)")
        elif ratio > 1.5:
            logger.info(f"Performance note: {operation_type} for {table_name} took {time_taken:.1f}s "
                      f"({ratio:.1f}x longer than expected for {profile_name} profile)")

    def record_total_time(self, table_name: str, total_time: float, rows: int) -> None:
        """
        Record total processing time for a table.

        Args:
            table_name: Name of the table
            total_time: Total processing time in seconds
            rows: Number of rows processed
        """
        if not self.enabled:
            return

        with self.metrics_lock:
            # Create table metrics dictionary if it doesn't exist
            if table_name not in self.table_metrics:
                self.table_metrics[table_name] = {}

            # Create total time metrics for this table if they don't exist
            if "total_time" not in self.table_metrics[table_name]:
                self.table_metrics[table_name]["total_time"] = PerformanceMetric(
                    "total_time", "seconds", f"Total processing time for {table_name}"
                )

            if "total_rate" not in self.table_metrics[table_name]:
                self.table_metrics[table_name]["total_rate"] = PerformanceMetric(
                    "total_rate", "rows/s", f"Overall processing rate for {table_name}"
                )

            # Record metrics
            self.table_metrics[table_name]["total_time"].record(total_time)

            # Calculate and record total rate
            if total_time > 0:
                total_rate = rows / total_time
                self.table_metrics[table_name]["total_rate"].record(total_rate)

            # Check for alerts
            total_threshold = self.alert_thresholds.get("total_time_seconds", 3600)
            if total_time >= total_threshold:
                logger.warning(f"ALERT: Long processing time for {table_name}: {total_time:.1f}s (threshold: {total_threshold}s)")

    def get_table_metrics(self, table_name: str) -> Dict[str, Dict[str, Any]]:
        """
        Get performance metrics for a specific table.

        Args:
            table_name: Name of the table

        Returns:
            Dictionary with metrics for the table
        """
        if not self.enabled or table_name not in self.table_metrics:
            return {}

        with self.metrics_lock:
            return {
                metric_name: metric.get_statistics()
                for metric_name, metric in self.table_metrics[table_name].items()
            }

    def get_resource_metrics(self) -> Dict[str, Dict[str, Any]]:
        """
        Get system resource metrics.

        Returns:
            Dictionary with resource metrics
        """
        if not self.enabled or not self.resource_monitoring:
            return {}

        with self.resource_lock:
            return {
                metric_name: metric.get_statistics()
                for metric_name, metric in self.resource_metrics.items()
            }

    def get_all_metrics(self) -> Dict[str, Any]:
        """
        Get all performance metrics.

        Returns:
            Dictionary with all metrics
        """
        if not self.enabled:
            return {}

        # Calculate overall duration
        duration = time.time() - self.start_time

        # Get resource metrics
        resource_metrics = self.get_resource_metrics()

        # Get table metrics
        table_metrics = {}
        with self.metrics_lock:
            for table_name, metrics in self.table_metrics.items():
                table_metrics[table_name] = {
                    metric_name: metric.get_statistics()
                    for metric_name, metric in metrics.items()
                }

        # Create summary metrics
        summary = {
            "start_time": datetime.datetime.fromtimestamp(self.start_time).isoformat(),
            "end_time": datetime.datetime.fromtimestamp(time.time()).isoformat(),
            "duration_seconds": duration,
            "total_tables": len(table_metrics),
            "resource_metrics": resource_metrics,
            "table_metrics": table_metrics
        }

        return summary

    def generate_performance_report(self) -> Dict[str, Any]:
        """
        Generate a comprehensive performance report.

        Returns:
            Dictionary with performance report data
        """
        if not self.enabled:
            return {}

        # Get all metrics
        all_metrics = self.get_all_metrics()

        # Add additional report information
        report = {
            "report_type": "performance_report",
            "generated_at": datetime.datetime.now().isoformat(),
            "metrics": all_metrics
        }

        # Calculate aggregate statistics
        if all_metrics.get("table_metrics"):
            # Calculate average query time across all tables
            query_times = []
            processing_times = []
            upload_times = []
            total_times = []

            for table_name, metrics in all_metrics["table_metrics"].items():
                if "query_time" in metrics and metrics["query_time"].get("mean") is not None:
                    query_times.append(metrics["query_time"]["mean"])

                if "processing_time" in metrics and metrics["processing_time"].get("mean") is not None:
                    processing_times.append(metrics["processing_time"]["mean"])

                if "upload_time" in metrics and metrics["upload_time"].get("mean") is not None:
                    upload_times.append(metrics["upload_time"]["mean"])

                if "total_time" in metrics and metrics["total_time"].get("mean") is not None:
                    total_times.append(metrics["total_time"]["mean"])

            # Add aggregate statistics to report
            report["aggregate_statistics"] = {
                "average_query_time": statistics.mean(query_times) if query_times else None,
                "average_processing_time": statistics.mean(processing_times) if processing_times else None,
                "average_upload_time": statistics.mean(upload_times) if upload_times else None,
                "average_total_time": statistics.mean(total_times) if total_times else None,
                "max_query_time": max(query_times) if query_times else None,
                "max_processing_time": max(processing_times) if processing_times else None,
                "max_upload_time": max(upload_times) if upload_times else None,
                "max_total_time": max(total_times) if total_times else None
            }

        return report

    def save_performance_report(self) -> str:
        """
        Generate and save a performance report to a file.

        Returns:
            Path to the saved report file
        """
        if not self.enabled or not self.save_reports:
            return ""

        # Generate the report
        report = self.generate_performance_report()

        # Get the report directory and template
        report_dir = self.config_manager.get('performance_monitoring', 'report_dir', 'logs/performance')
        report_template = self.config_manager.get('performance_monitoring', 'report_template', 'performance_report_{date_str}.json')

        # Create the report directory if it doesn't exist
        os.makedirs(report_dir, exist_ok=True)

        # Format the report filename
        date_str = datetime.datetime.now().strftime('%Y-%m-%d_%H%M%S')
        report_filename = report_template.format(date_str=date_str)
        report_path = os.path.join(report_dir, report_filename)

        # Save the report
        try:
            with open(report_path, 'w') as f:
                json.dump(report, f, indent=2)
            logger.info(f"Performance report saved to {report_path}")
            return report_path
        except Exception as e:
            logger.error(f"Error saving performance report: {str(e)}")
            return ""

    def save_table_performance_report(self, table_name: str) -> str:
        """
        Generate and save a performance report for a specific table.

        Args:
            table_name: Name of the table

        Returns:
            Path to the saved report file
        """
        if not self.enabled or not self.save_reports or table_name not in self.table_metrics:
            return ""

        # Get table metrics
        table_metrics = self.get_table_metrics(table_name)

        # Get resource metrics
        resource_metrics = self.get_resource_metrics()

        # Create the report
        report = {
            "report_type": "table_performance_report",
            "generated_at": datetime.datetime.now().isoformat(),
            "table_name": table_name,
            "metrics": {
                "table_metrics": table_metrics,
                "resource_metrics": resource_metrics
            }
        }

        # Get the report directory and template
        report_dir = self.config_manager.get('performance_monitoring', 'report_dir', 'logs/performance')
        report_template = self.config_manager.get('performance_monitoring', 'table_report_template', 'performance_report_{table_name}_{date_str}.json')

        # Create the report directory if it doesn't exist
        os.makedirs(report_dir, exist_ok=True)

        # Format the report filename
        date_str = datetime.datetime.now().strftime('%Y-%m-%d_%H%M%S')
        report_filename = report_template.format(table_name=table_name, date_str=date_str)
        report_path = os.path.join(report_dir, report_filename)

        # Save the report
        try:
            with open(report_path, 'w') as f:
                json.dump(report, f, indent=2)
            logger.info(f"Table performance report saved to {report_path}")
            return report_path
        except Exception as e:
            logger.error(f"Error saving table performance report: {str(e)}")
            return ""

    def __enter__(self):
        """Context manager entry point."""
        self.start_monitoring()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit point."""
        self.stop_monitoring()

        # Save final performance report if enabled
        if self.enabled and self.save_reports:
            self.save_performance_report()
