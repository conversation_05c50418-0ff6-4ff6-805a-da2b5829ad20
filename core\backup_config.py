#!/usr/bin/env python3
"""
Backup Configuration Module

This module provides a configuration class for the backup manager.
It centralizes all configuration parameters and provides defaults.
"""

import datetime
import logging
from typing import Optional
from dataclasses import dataclass

from core.config_manager import ConfigManager
from utils.input_validation import validate_date, validate_numeric_range

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class BackupConfig:
    """
    Configuration class for the backup manager.
    Centralizes all configuration parameters with proper defaults from ConfigManager.
    """
    # Initialize config_manager first so we can use it for defaults
    config_manager: Optional[ConfigManager] = None

    # Get default values from ConfigManager
    def _get_default(self, section, key, default_value, value_type=None):
        """Get default value from ConfigManager if available, otherwise use provided default"""
        if self.config_manager is None:
            self.config_manager = ConfigManager()
        return self.config_manager.get(section, key, default_value, value_type)

    # Basic backup settings - defaults will be loaded from ConfigManager in __post_init__
    days: int = None
    chunk_size: int = None
    max_retries: int = None
    timeout: int = None
    max_threads: int = None
    tables: list = None

    # Mode settings
    specific_date: Optional[datetime.datetime] = None
    incremental: bool = False
    validate: bool = None

    # Monthly backup settings
    month: Optional[int] = None
    year: Optional[int] = None
    monthly_chunking_strategy: Optional[str] = None
    monthly_max_chunk_retries: Optional[int] = None
    monthly_retry_delay: Optional[int] = None

    # Parallel processing settings
    parallel: bool = False
    max_concurrent_tables: int = None
    batch_size: int = None
    table_timeout: int = None  # Timeout for individual table processing in seconds

    # Adaptive chunking settings
    adaptive_chunking: bool = None
    min_chunk_size: int = None
    max_chunk_size: int = None
    target_memory_percent: int = None
    critical_memory_percent: int = None
    memory_check_interval: int = None
    auto_continue_chunks: bool = None

    # Advanced parallel processing settings
    adaptive_thread_pool: bool = None
    thread_health_check_interval: int = None
    thread_health_timeout: int = None
    work_stealing: bool = None
    resource_monitoring_interval: int = None

    # Progressive validation settings
    progressive_validation: bool = None
    validation_interval: int = None
    validate_during_retrieval: bool = None
    validate_before_upload: bool = None
    validate_after_upload: bool = None
    retry_failed_validations: bool = None
    max_validation_retries: int = None

    # Performance monitoring settings
    performance_monitoring: bool = None
    metrics_collection_interval: int = None
    detailed_metrics: bool = None
    resource_monitoring: bool = None
    query_performance_tracking: bool = None
    storage_performance_tracking: bool = None
    thread_performance_tracking: bool = None
    save_performance_reports: bool = None

    def __post_init__(self):
        """Initialize with values from config.json if available."""
        if self.config_manager is None:
            self.config_manager = ConfigManager()

        # Load values from config if not already set
        # Use default values from ConfigManager.DEFAULT_CONFIG
        self.days = self._get_default('backup', 'default_days', 1, int) if self.days is None else self.days
        self.chunk_size = self._get_default('backup', 'default_chunk_size', 500000, int) if self.chunk_size is None else self.chunk_size
        self.max_retries = self._get_default('backup', 'default_max_retries', 5, int) if self.max_retries is None else self.max_retries
        self.timeout = self._get_default('backup', 'default_timeout', 1800, int) if self.timeout is None else self.timeout
        self.max_threads = self._get_default('backup', 'default_max_threads', 8, int) if self.max_threads is None else self.max_threads
        self.max_concurrent_tables = self._get_default('backup', 'default_max_concurrent_tables', 3, int) if self.max_concurrent_tables is None else self.max_concurrent_tables
        self.batch_size = self._get_default('backup', 'default_batch_size', 10, int) if self.batch_size is None else self.batch_size
        self.table_timeout = self._get_default('backup', 'table_timeout', 7200, int) if self.table_timeout is None else self.table_timeout
        self.validate = self._get_default('backup', 'validate_backups', True, bool) if self.validate is None else self.validate

        # Initialize adaptive chunking settings
        self.adaptive_chunking = self._get_default('storage', 'adaptive_compression', {}).get('enabled', True) if self.adaptive_chunking is None else self.adaptive_chunking
        self.min_chunk_size = self._get_default('storage', 'adaptive_compression', {}).get('min_chunk_size', 50000) if self.min_chunk_size is None else self.min_chunk_size
        self.max_chunk_size = self._get_default('storage', 'adaptive_compression', {}).get('max_chunk_size', 2000000) if self.max_chunk_size is None else self.max_chunk_size
        self.target_memory_percent = self._get_default('storage', 'adaptive_compression', {}).get('target_memory_percent', 70) if self.target_memory_percent is None else self.target_memory_percent
        self.critical_memory_percent = self._get_default('storage', 'adaptive_compression', {}).get('critical_memory_percent', 85) if self.critical_memory_percent is None else self.critical_memory_percent
        self.memory_check_interval = self._get_default('storage', 'adaptive_compression', {}).get('memory_check_interval', 5) if self.memory_check_interval is None else self.memory_check_interval
        self.auto_continue_chunks = self._get_default('backup', 'auto_continue_chunks', True) if self.auto_continue_chunks is None else self.auto_continue_chunks

        # Initialize advanced parallel processing settings
        self.adaptive_thread_pool = self._get_default('backup', 'parallel_processing', {}).get('adaptive_thread_pool', True) if self.adaptive_thread_pool is None else self.adaptive_thread_pool
        self.thread_health_check_interval = self._get_default('backup', 'parallel_processing', {}).get('thread_health_check_interval', 30) if self.thread_health_check_interval is None else self.thread_health_check_interval
        self.thread_health_timeout = self._get_default('backup', 'parallel_processing', {}).get('thread_health_timeout', 300) if self.thread_health_timeout is None else self.thread_health_timeout
        self.work_stealing = self._get_default('backup', 'parallel_processing', {}).get('work_stealing', True) if self.work_stealing is None else self.work_stealing
        self.resource_monitoring_interval = self._get_default('backup', 'parallel_processing', {}).get('resource_monitoring_interval', 15) if self.resource_monitoring_interval is None else self.resource_monitoring_interval

        # Initialize progressive validation settings
        self.progressive_validation = self._get_default('validation', 'progressive_validation', True) if self.progressive_validation is None else self.progressive_validation
        self.validation_interval = self._get_default('validation', 'validation_interval', 50000) if self.validation_interval is None else self.validation_interval
        self.validate_during_retrieval = self._get_default('validation', 'validate_during_retrieval', True) if self.validate_during_retrieval is None else self.validate_during_retrieval
        self.validate_before_upload = self._get_default('validation', 'validate_before_upload', True) if self.validate_before_upload is None else self.validate_before_upload
        self.validate_after_upload = self._get_default('validation', 'validate_after_upload', True) if self.validate_after_upload is None else self.validate_after_upload
        self.retry_failed_validations = self._get_default('validation', 'retry_failed_validations', True) if self.retry_failed_validations is None else self.retry_failed_validations
        self.max_validation_retries = self._get_default('validation', 'max_validation_retries', 3) if self.max_validation_retries is None else self.max_validation_retries

        # Initialize monthly backup settings
        self.monthly_chunking_strategy = self._get_default('backup', 'monthly_chunking_strategy', 'week') if self.monthly_chunking_strategy is None else self.monthly_chunking_strategy
        self.monthly_max_chunk_retries = self._get_default('backup', 'monthly_max_chunk_retries', 3) if self.monthly_max_chunk_retries is None else self.monthly_max_chunk_retries
        self.monthly_retry_delay = self._get_default('backup', 'monthly_retry_delay', 60) if self.monthly_retry_delay is None else self.monthly_retry_delay

        # Initialize performance monitoring settings
        self.performance_monitoring = self._get_default('performance_monitoring', 'enabled', True) if self.performance_monitoring is None else self.performance_monitoring
        self.metrics_collection_interval = self._get_default('performance_monitoring', 'metrics_collection_interval', 5) if self.metrics_collection_interval is None else self.metrics_collection_interval
        self.detailed_metrics = self._get_default('performance_monitoring', 'detailed_metrics', True) if self.detailed_metrics is None else self.detailed_metrics
        self.resource_monitoring = self._get_default('performance_monitoring', 'resource_monitoring', True) if self.resource_monitoring is None else self.resource_monitoring
        self.query_performance_tracking = self._get_default('performance_monitoring', 'query_performance_tracking', True) if self.query_performance_tracking is None else self.query_performance_tracking
        self.storage_performance_tracking = self._get_default('performance_monitoring', 'storage_performance_tracking', True) if self.storage_performance_tracking is None else self.storage_performance_tracking
        self.thread_performance_tracking = self._get_default('performance_monitoring', 'thread_performance_tracking', True) if self.thread_performance_tracking is None else self.thread_performance_tracking
        self.save_performance_reports = self._get_default('performance_monitoring', 'save_performance_reports', True) if self.save_performance_reports is None else self.save_performance_reports

    @classmethod
    def from_args(cls, args, config_manager=None):
        """
        Create a configuration object from command line arguments.

        Args:
            args: Command line arguments
            config_manager: Optional ConfigManager instance

        Returns:
            BackupConfig instance
        """
        config = cls(config_manager=config_manager)

        # Validate and override with command line arguments if provided
        if args.days is not None:
            is_valid, value, error = validate_numeric_range(args.days, 1, 365, "days")
            if is_valid:
                config.days = value
            else:
                logger.warning(f"Invalid days value: {error}. Using default: {config.days}")

        if args.chunk_size is not None:
            is_valid, value, error = validate_numeric_range(args.chunk_size, 1000, 10000000, "chunk_size")
            if is_valid:
                config.chunk_size = value
            else:
                logger.warning(f"Invalid chunk_size value: {error}. Using default: {config.chunk_size}")

        if args.max_retries is not None:
            is_valid, value, error = validate_numeric_range(args.max_retries, 0, 20, "max_retries")
            if is_valid:
                config.max_retries = value
            else:
                logger.warning(f"Invalid max_retries value: {error}. Using default: {config.max_retries}")

        if args.timeout is not None:
            is_valid, value, error = validate_numeric_range(args.timeout, 60, 7200, "timeout")
            if is_valid:
                config.timeout = value
            else:
                logger.warning(f"Invalid timeout value: {error}. Using default: {config.timeout}")

        if args.max_threads is not None:
            is_valid, value, error = validate_numeric_range(args.max_threads, 1, 32, "max_threads")
            if is_valid:
                config.max_threads = value
            else:
                logger.warning(f"Invalid max_threads value: {error}. Using default: {config.max_threads}")

        if args.max_concurrent_tables is not None:
            is_valid, value, error = validate_numeric_range(args.max_concurrent_tables, 1, 10, "max_concurrent_tables")
            if is_valid:
                config.max_concurrent_tables = value
            else:
                logger.warning(f"Invalid max_concurrent_tables value: {error}. Using default: {config.max_concurrent_tables}")

        if args.batch_size is not None:
            is_valid, value, error = validate_numeric_range(args.batch_size, 1, 100, "batch_size")
            if is_valid:
                config.batch_size = value
            else:
                logger.warning(f"Invalid batch_size value: {error}. Using default: {config.batch_size}")

        if args.table_timeout is not None:
            is_valid, value, error = validate_numeric_range(args.table_timeout, 300, 86400, "table_timeout")
            if is_valid:
                config.table_timeout = value
                logger.info(f"Using table timeout: {value} seconds")
            else:
                logger.warning(f"Invalid table_timeout value: {error}. Using default: {config.table_timeout}")

        # Set mode-specific settings
        if args.mode == 'specific':
            # Set default to yesterday
            yesterday = datetime.datetime.now() - datetime.timedelta(days=1)

            if args.date:
                is_valid, parsed_date, error = validate_date(args.date)
                if is_valid:
                    config.specific_date = parsed_date
                else:
                    logger.warning(f"{error}. Using yesterday's date: {yesterday.strftime('%Y-%m-%d')}")
                    config.specific_date = yesterday
            else:
                # Default to yesterday if not specified
                config.specific_date = yesterday
                logger.info(f"No date specified, using yesterday: {yesterday.strftime('%Y-%m-%d')}")

        elif args.mode == 'incremental':
            config.incremental = True

        elif args.mode == 'parallel':
            config.parallel = True

        elif args.mode == 'monthly':
            # Set month and year for monthly backup
            if args.month:
                is_valid, value, error = validate_numeric_range(args.month, 1, 12, "month")
                if is_valid:
                    config.month = value
                else:
                    logger.warning(f"Invalid month value: {error}. Using current month.")
                    config.month = datetime.datetime.now().month
            else:
                # Default to previous month if not specified
                prev_month = datetime.datetime.now().replace(day=1) - datetime.timedelta(days=1)
                config.month = prev_month.month
                logger.info(f"No month specified, using previous month: {prev_month.strftime('%B')}")

            if args.year:
                is_valid, value, error = validate_numeric_range(args.year, 2000, 2100, "year")
                if is_valid:
                    config.year = value
                else:
                    logger.warning(f"Invalid year value: {error}. Using current year.")
                    config.year = datetime.datetime.now().year
            else:
                # Default to current year if not specified
                config.year = datetime.datetime.now().year
                logger.info(f"No year specified, using current year: {config.year}")

            # Set chunking strategy if specified
            if args.chunking_strategy:
                if args.chunking_strategy in ['week', 'day']:
                    config.monthly_chunking_strategy = args.chunking_strategy
                    logger.info(f"Using {args.chunking_strategy} chunking strategy for monthly backup")
                else:
                    logger.warning(f"Invalid chunking strategy: {args.chunking_strategy}. Using default: {config.monthly_chunking_strategy}")
            else:
                logger.info(f"No chunking strategy specified, using default: {config.monthly_chunking_strategy}")

            # Set monthly retry settings if specified
            if args.monthly_max_retries:
                is_valid, value, error = validate_numeric_range(args.monthly_max_retries, 0, 10, "monthly_max_retries")
                if is_valid:
                    config.monthly_max_chunk_retries = value
                    logger.info(f"Using monthly max retries: {value}")
                else:
                    logger.warning(f"Invalid monthly max retries value: {error}. Using default: {config.monthly_max_chunk_retries}")

            if args.monthly_retry_delay:
                is_valid, value, error = validate_numeric_range(args.monthly_retry_delay, 10, 600, "monthly_retry_delay")
                if is_valid:
                    config.monthly_retry_delay = value
                    logger.info(f"Using monthly retry delay: {value} seconds")
                else:
                    logger.warning(f"Invalid monthly retry delay value: {error}. Using default: {config.monthly_retry_delay}")

            # Enable parallel processing for monthly backups by default
            config.parallel = True
            logger.info(f"Monthly backup mode enabled for {datetime.datetime(config.year, config.month, 1).strftime('%B %Y')}")

        # Handle tables parameter
        if args.tables:
            config.tables = [table.strip() for table in args.tables.split(',')]
            logger.info(f"Using specified tables: {', '.join(config.tables)}")
        else:
            config.tables = []

        return config

    def get(self, key, default=None):
        """
        Get a configuration value by key.

        Args:
            key: Configuration key
            default: Default value if key is not found

        Returns:
            Configuration value or default
        """
        if hasattr(self, key):
            value = getattr(self, key)
            if value is not None:
                return value
        return default
