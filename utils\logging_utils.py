"""
Minimalist Logging Utility

A simplified logging system that focuses on essential information while reducing noise.
"""

import os
import logging
import logging.handlers
import datetime
from typing import Optional

# Constants
DEFAULT_LOG_DIR = 'logs'
DEFAULT_LOG_FILE = 'backup.log'
DEFAULT_MAX_SIZE_MB = 10
DEFAULT_MAX_FILES = 5

class MinimalLogger:
    """
    Minimalist logger that focuses on essential information.
    """
    
    def __init__(self):
        self._logger = None
        self._setup_logger()

    def _setup_logger(self):
        """Configure the minimal logger."""
        # Create logs directory if it doesn't exist
        os.makedirs(DEFAULT_LOG_DIR, exist_ok=True)

        # Create root logger
        logger = logging.getLogger()
        logger.setLevel(logging.INFO)

        # Remove any existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # Console handler with minimal formatting
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter('[%(levelname)s] %(message)s'))
        logger.addHandler(console_handler)

        # File handler with rotation
        log_file = os.path.join(DEFAULT_LOG_DIR, DEFAULT_LOG_FILE)
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=DEFAULT_MAX_SIZE_MB * 1024 * 1024,
            backupCount=DEFAULT_MAX_FILES
        )
        file_handler.setFormatter(
            logging.Formatter('%(asctime)s [%(levelname)s] %(message)s', 
                            datefmt='%Y-%m-%d %H:%M:%S')
        )
        logger.addHandler(file_handler)

        self._logger = logger

    def info(self, message: str) -> None:
        """Log info message."""
        self._logger.info(message)

    def error(self, message: str) -> None:
        """Log error message."""
        self._logger.error(message)

    def warning(self, message: str) -> None:
        """Log warning message."""
        self._logger.warning(message)

    def log_operation(self, operation: str, status: str, details: Optional[str] = None) -> None:
        """
        Log an operation with its status and optional details.
        
        Args:
            operation: Name of the operation
            status: Status (SUCCESS, FAILED, etc.)
            details: Optional additional details
        """
        message = f"{operation}: {status}"
        if details:
            message += f" - {details}"
        
        if status == "FAILED":
            self._logger.error(message)
        elif status == "WARNING":
            self._logger.warning(message)
        else:
            self._logger.info(message)

    def log_backup_summary(self, successful: int, failed: int, duration: float) -> None:
        """
        Log a concise backup summary.
        
        Args:
            successful: Number of successful operations
            failed: Number of failed operations
            duration: Duration in seconds
        """
        total = successful + failed
        success_rate = (successful / total * 100) if total > 0 else 0
        
        self._logger.info("-" * 50)
        self._logger.info("Backup Summary:")
        self._logger.info(f"Success Rate: {success_rate:.1f}% ({successful}/{total})")
        self._logger.info(f"Duration: {self._format_duration(duration)}")
        if failed > 0:
            self._logger.warning(f"Failed Operations: {failed}")
        self._logger.info("-" * 50)

    @staticmethod
    def _format_duration(seconds: float) -> str:
        """Format duration in a human-readable format."""
        if seconds < 60:
            return f"{seconds:.1f} seconds"
        elif seconds < 3600:
            minutes = seconds // 60
            remaining_seconds = seconds % 60
            return f"{int(minutes)}m {int(remaining_seconds)}s"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{int(hours)}h {int(minutes)}m"

# Global logger instance
logger = MinimalLogger()
