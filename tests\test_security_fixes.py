#!/usr/bin/env python3
"""
Security Tests for TNGD Backup System

This module tests the security fixes implemented to address critical vulnerabilities:
1. SQL Injection prevention
2. Credential exposure prevention

SECURITY HOTFIX VERIFICATION
"""

import unittest
import os
import sys
from unittest.mock import patch, MagicMock
import tempfile
import logging

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.devo_client import DevoClient
from core.config_manager import ConfigManager
from core.storage_manager import StorageManager


class TestSQLInjectionPrevention(unittest.TestCase):
    """Test SQL injection prevention measures."""

    def setUp(self):
        """Set up test fixtures."""
        # Mock environment variables for testing
        self.env_patcher = patch.dict(os.environ, {
            'DEVO_API_KEY': 'test_key',
            'DEVO_API_SECRET': 'test_secret',
            'DEVO_QUERY_ENDPOINT': 'https://test.devo.com'
        })
        self.env_patcher.start()

    def tearDown(self):
        """Clean up test fixtures."""
        self.env_patcher.stop()

    def test_table_name_sanitization_blocks_injection(self):
        """Test that malicious table names are blocked."""
        client = DevoClient()
        
        # Test various SQL injection patterns
        malicious_table_names = [
            "table'; DROP TABLE users; --",
            "table UNION SELECT * FROM passwords",
            "table; DELETE FROM logs",
            "table/* comment */",
            "table OR 1=1",
            "table' AND password='admin",
        ]
        
        for malicious_name in malicious_table_names:
            with self.assertRaises(ValueError, msg=f"Should reject malicious table name: {malicious_name}"):
                client._sanitize_table_name(malicious_name)

    def test_table_name_sanitization_allows_valid_names(self):
        """Test that valid table names are allowed."""
        client = DevoClient()
        
        valid_table_names = [
            "my.app.tngd.table",
            "simple_table",
            "table-with-hyphens",
            "table123",
            "my_app_table_2024"
        ]
        
        for valid_name in valid_table_names:
            try:
                result = client._sanitize_table_name(valid_name)
                self.assertIsInstance(result, str)
                self.assertTrue(len(result) > 0)
            except ValueError as e:
                self.fail(f"Should allow valid table name '{valid_name}': {e}")

    def test_where_clause_validation_blocks_injection(self):
        """Test that malicious WHERE clauses are blocked."""
        client = DevoClient()
        
        malicious_where_clauses = [
            "where 1=1; DROP TABLE users",
            "where id=1 UNION SELECT * FROM passwords",
            "where name='test'; DELETE FROM logs",
            "where /* comment */ id=1",
        ]
        
        for malicious_clause in malicious_where_clauses:
            with self.assertRaises(ValueError, msg=f"Should reject malicious WHERE clause: {malicious_clause}"):
                client._validate_where_clause(malicious_clause)

    def test_secure_query_builder_validates_parameters(self):
        """Test that the secure query builder validates all parameters."""
        client = DevoClient()
        
        # Test invalid limit values
        with self.assertRaises(ValueError):
            client._build_secure_query("test_table", limit=-1)
        
        with self.assertRaises(ValueError):
            client._build_secure_query("test_table", limit=99999999)
        
        # Test invalid offset values
        with self.assertRaises(ValueError):
            client._build_secure_query("test_table", offset=-1)
        
        # Test valid parameters
        try:
            query = client._build_secure_query(
                "test_table", 
                where_clause="where eventdate > '2024-01-01'",
                limit=1000,
                offset=0
            )
            self.assertIsInstance(query, str)
            self.assertIn("test_table", query)
        except Exception as e:
            self.fail(f"Should allow valid parameters: {e}")


class TestCredentialExposurePrevention(unittest.TestCase):
    """Test credential exposure prevention measures."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a temporary log file for testing
        self.temp_log = tempfile.NamedTemporaryFile(mode='w+', delete=False)
        self.temp_log.close()
        
        # Set up logging to capture log messages
        self.log_handler = logging.FileHandler(self.temp_log.name)
        self.logger = logging.getLogger()
        self.logger.addHandler(self.log_handler)
        self.logger.setLevel(logging.DEBUG)

    def tearDown(self):
        """Clean up test fixtures."""
        self.logger.removeHandler(self.log_handler)
        self.log_handler.close()
        os.unlink(self.temp_log.name)

    @patch.dict(os.environ, {
        'SMTP_PASSWORD': 'secret_password_123',
        'SMTP_SERVER': 'smtp.test.com',
        'SMTP_PORT': '587',
        'SMTP_SENDER': '<EMAIL>',
        'SMTP_RECEIVER': '<EMAIL>'
    })
    def test_smtp_password_not_logged(self):
        """Test that SMTP passwords are not logged."""
        config = ConfigManager()
        email_settings = config.get_email_settings()
        
        # Read the log file
        with open(self.temp_log.name, 'r') as f:
            log_content = f.read()
        
        # Verify password is not in logs
        self.assertNotIn('secret_password_123', log_content)
        self.assertNotIn('password', log_content.lower())

    @patch.dict(os.environ, {
        'OSS_ACCESS_KEY_ID': 'test_access_key',
        'OSS_ACCESS_KEY_SECRET': 'test_secret_key',
        'OSS_ENDPOINT': 'https://oss.test.com',
        'OSS_BUCKET': 'test-bucket'
    })
    def test_oss_credentials_not_stored_as_instance_variables(self):
        """Test that OSS credentials are not stored as instance variables."""
        config = ConfigManager()
        storage = StorageManager(config)
        
        # Check that credentials are not stored as instance variables
        self.assertFalse(hasattr(storage, 'credentials'))
        self.assertFalse(hasattr(storage, 'access_key'))
        self.assertFalse(hasattr(storage, 'secret_key'))
        
        # Verify credentials can still be retrieved when needed
        credentials = storage._get_credentials()
        self.assertIsInstance(credentials, dict)
        self.assertIn('access_key_id', credentials)

    @patch.dict(os.environ, {
        'DEVO_API_KEY': 'test_api_key',
        'DEVO_API_SECRET': 'test_api_secret',
        'DEVO_QUERY_ENDPOINT': 'https://test.devo.com'
    })
    def test_devo_credentials_not_stored_as_instance_variables(self):
        """Test that Devo API credentials are not stored as instance variables."""
        client = DevoClient()
        
        # Check that credentials are not stored as instance variables
        self.assertFalse(hasattr(client, 'key'))
        self.assertFalse(hasattr(client, 'secret'))
        
        # Verify credentials can still be retrieved when needed
        credentials = client._get_credentials()
        self.assertIsInstance(credentials, dict)
        self.assertIn('key', credentials)
        self.assertIn('secret', credentials)


class TestTemporaryFileSecurity(unittest.TestCase):
    """Test temporary file security measures."""

    def test_storage_manager_uses_secure_temp_files(self):
        """Test that StorageManager uses secure temporary file creation."""
        from core.storage_manager import StorageManager
        from core.config_manager import ConfigManager

        # Mock the compression service to avoid actual compression
        with patch('core.storage_manager.CompressionService') as mock_compression, \
             patch('core.storage_manager.ChecksumService') as mock_checksum, \
             patch('core.storage_manager.oss2'):

            # Set up mocks
            mock_compression.return_value.compress_directory.return_value = (True, 'test_file.tar.gz', {})
            mock_checksum.return_value.calculate_file_checksum.return_value = 'test_checksum'

            config = ConfigManager()
            storage = StorageManager(config)

            # Mock the OSS operations
            with patch.object(storage, '_upload_file_to_oss', return_value=(True, {})), \
                 patch.object(storage, '_verify_uploaded_file', return_value=True), \
                 patch('os.path.exists', return_value=True), \
                 patch('os.remove') as mock_remove:

                # Test that compress_and_upload uses secure temp files
                result = storage.compress_and_upload('test_dir', 'test_path')

                # Verify the method completed successfully
                self.assertTrue(result[0])

                # Verify that temporary files were cleaned up
                mock_remove.assert_called()

    def test_verification_uses_secure_temp_files(self):
        """Test that file verification uses secure temporary files."""
        from core.storage_manager import StorageManager
        from core.config_manager import ConfigManager

        with patch('core.storage_manager.CompressionService'), \
             patch('core.storage_manager.ChecksumService') as mock_checksum, \
             patch('core.storage_manager.oss2'), \
             patch('tempfile.NamedTemporaryFile') as mock_temp:

            # Set up mocks
            mock_temp.return_value.__enter__.return_value.name = '/secure/temp/file.tmp'
            mock_checksum.return_value.calculate_file_checksum.return_value = 'test_checksum'

            config = ConfigManager()
            storage = StorageManager(config)

            # Mock OSS bucket operations
            with patch.object(storage, 'get_oss_bucket') as mock_bucket, \
                 patch('os.path.exists', return_value=True), \
                 patch('os.remove') as mock_remove:

                mock_bucket.return_value.get_object_to_file.return_value = None

                # Test verification
                result = storage._verify_uploaded_file('test_path', 'test_checksum')

                # Verify secure temporary file was used
                mock_temp.assert_called_once()
                # Verify cleanup occurred
                mock_remove.assert_called()

    def test_table_processor_uses_secure_temp_directories(self):
        """Test that table processor uses secure temporary directories."""
        from core.unified_table_processor import SmartProcessingStrategy
        from core.backup_config import BackupConfig

        with patch('tempfile.mkdtemp') as mock_mkdtemp, \
             patch('shutil.rmtree') as mock_rmtree, \
             patch('os.path.exists', return_value=True):

            mock_mkdtemp.return_value = '/secure/temp/dir'

            strategy = SmartProcessingStrategy()
            config = BackupConfig()

            # Mock all the dependencies
            with patch('core.unified_table_processor.DevoClient'), \
                 patch('core.unified_table_processor.StorageManager'), \
                 patch('core.unified_table_processor.ChunkManager') as mock_chunk, \
                 patch('core.unified_table_processor.ProgressReporter'):

                mock_chunk.return_value.query_and_save_data.return_value = (0, 0)  # No data

                try:
                    strategy._process_single_table(
                        'test.table', config, None, None, mock_chunk.return_value, None
                    )
                except:
                    pass  # We expect this to fail due to mocking, but we want to test temp dir usage

                # Verify secure temporary directory was created
                mock_mkdtemp.assert_called_once()
                # Verify cleanup was attempted
                mock_rmtree.assert_called()


class TestSecurityValidation(unittest.TestCase):
    """Test overall security validation."""

    def test_no_hardcoded_credentials_in_code(self):
        """Test that no hardcoded credentials exist in the codebase."""
        # This is a basic check - in a real environment, you'd scan all source files
        suspicious_patterns = [
            'password=',
            'secret=',
            'key=',
            'token=',
            'api_key=',
            'access_key='
        ]

        # Check this test file doesn't contain actual credentials
        with open(__file__, 'r') as f:
            content = f.read().lower()

        for pattern in suspicious_patterns:
            if pattern in content:
                # Allow test patterns but not real-looking credentials
                lines = [line for line in content.split('\n') if pattern in line.lower()]
                for line in lines:
                    # Skip test data, comments, and malicious examples
                    if ('test_' in line or '#' in line or 'mock' in line.lower() or
                        'example' in line.lower() or 'dummy' in line.lower() or
                        'malicious' in line.lower() or '"' in line or "'" in line):
                        continue
                    self.fail(f"Potential hardcoded credential found: {line.strip()}")


if __name__ == '__main__':
    # Run the security tests
    unittest.main(verbosity=2)
