#!/usr/bin/env python3
"""
Batch Security Helper for TNGD Backup System

This utility provides secure operations for batch scripts, including:
- Secure temporary file creation
- Input validation and sanitization
- Path resolution using ConfigManager
- Parameter validation

This follows TNGD security patterns and integrates with existing infrastructure.
"""

import os
import sys
import tempfile
import argparse
from datetime import datetime
from typing import Tuple, List
import re

# Add the project root to the path to import TNGD modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from core.config_manager import ConfigManager
    from utils.input_validation import validate_date
    from utils.logging_utils import MinimalLogger
except ImportError as e:
    print(f"ERROR: Failed to import TNGD modules: {e}", file=sys.stderr)
    sys.exit(1)

# Initialize logger
logger = MinimalLogger()

class BatchSecurityHelper:
    """Security helper for batch script operations."""
    
    def __init__(self):
        """Initialize the security helper."""
        self.config_manager = ConfigManager()
    
    def create_secure_log_file(self, prefix: str = "tngd_backup", suffix: str = ".log") -> str:
        """
        Create a secure temporary log file.
        
        Args:
            prefix: File prefix
            suffix: File suffix
            
        Returns:
            Path to the secure temporary file
        """
        try:
            # Create secure temporary file
            fd, temp_path = tempfile.mkstemp(suffix=suffix, prefix=f"{prefix}_")
            os.close(fd)  # Close the file descriptor, we only need the path
            
            logger.info(f"Created secure temporary log file: {temp_path}")
            return temp_path
            
        except Exception as e:
            logger.error(f"Failed to create secure temporary file: {e}")
            # Fallback to a timestamped file in logs directory
            log_dir = self.config_manager.get('logging', 'log_dir', 'logs')
            os.makedirs(log_dir, exist_ok=True)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]  # Include milliseconds
            fallback_path = os.path.join(log_dir, f"{prefix}_{timestamp}{suffix}")
            return fallback_path
    
    def validate_date_parameter(self, date_str: str) -> Tuple[bool, str]:
        """
        Validate a date parameter using TNGD validation patterns.
        
        Args:
            date_str: Date string to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not date_str:
            return False, "Date parameter cannot be empty"
        
        # Use TNGD validation utility
        is_valid, _, error_msg = validate_date(date_str)
        if not is_valid:
            return False, error_msg
        
        # Additional security checks
        if not re.match(r'^\d{4}-\d{2}-\d{2}$', date_str):
            return False, f"Date format must be YYYY-MM-DD, got: {date_str}"
        
        return True, ""
    
    def sanitize_parameters(self, params: List[str]) -> List[str]:
        """
        Sanitize command parameters to prevent injection.
        
        Args:
            params: List of parameters to sanitize
            
        Returns:
            List of sanitized parameters
        """
        sanitized = []
        safe_pattern = re.compile(r'^[a-zA-Z0-9\-_.:/\\]+$')
        
        for param in params:
            if not param:
                continue
                
            # Check for dangerous patterns
            dangerous_patterns = [
                r'[;&|`$()]',  # Command separators and execution
                r'\.\./',      # Directory traversal
                r'<|>',        # Redirection
                r'\*|\?',      # Wildcards that could be dangerous
            ]
            
            is_dangerous = any(re.search(pattern, param) for pattern in dangerous_patterns)
            
            if is_dangerous:
                logger.warning(f"Skipping potentially dangerous parameter: {param}")
                continue
            
            # Only allow safe characters
            if safe_pattern.match(param):
                sanitized.append(param)
            else:
                logger.warning(f"Skipping parameter with unsafe characters: {param}")
        
        return sanitized
    
    def get_configured_path(self, path_type: str, default: str = "") -> str:
        """
        Get a configured path using ConfigManager.
        
        Args:
            path_type: Type of path (log_dir, temp_dir, etc.)
            default: Default value if not configured
            
        Returns:
            Configured path
        """
        try:
            if path_type == "log_dir":
                return self.config_manager.get('logging', 'log_dir', default or 'logs')
            elif path_type == "temp_dir":
                return self.config_manager.get('storage', 'temp_dir', default or 'temp')
            elif path_type == "script_dir":
                return "scripts"  # Scripts directory is standard
            else:
                logger.warning(f"Unknown path type: {path_type}")
                return default
        except Exception as e:
            logger.error(f"Failed to get configured path for {path_type}: {e}")
            return default
    
    def validate_month_year(self, month: str, year: str) -> Tuple[bool, str, int, int]:
        """
        Validate month and year parameters.
        
        Args:
            month: Month name or number
            year: Year string
            
        Returns:
            Tuple of (is_valid, error_message, month_num, year_num)
        """
        # Validate year
        try:
            year_num = int(year)
            if year_num < 2020 or year_num > 2030:  # Reasonable range
                return False, f"Year must be between 2020 and 2030, got: {year}", 0, 0
        except ValueError:
            return False, f"Invalid year format: {year}", 0, 0
        
        # Validate month
        month_names = {
            'january': 1, 'february': 2, 'march': 3, 'april': 4,
            'may': 5, 'june': 6, 'july': 7, 'august': 8,
            'september': 9, 'october': 10, 'november': 11, 'december': 12
        }
        
        month_lower = month.lower()
        if month_lower in month_names:
            month_num = month_names[month_lower]
        else:
            try:
                month_num = int(month)
                if month_num < 1 or month_num > 12:
                    return False, f"Month number must be between 1 and 12, got: {month}", 0, 0
            except ValueError:
                return False, f"Invalid month format: {month}", 0, 0
        
        return True, "", month_num, year_num
    
    def calculate_leap_year_days(self, month: int, year: int) -> int:
        """
        Calculate the correct number of days in February for leap years.
        
        Args:
            month: Month number (1-12)
            year: Year number
            
        Returns:
            Number of days in the month
        """
        if month != 2:
            # Not February, return standard days
            days_in_month = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
            return days_in_month[month - 1]
        
        # February - check for leap year
        # Correct leap year calculation:
        # - Divisible by 4: leap year
        # - Divisible by 100: not a leap year
        # - Divisible by 400: leap year
        if year % 400 == 0:
            return 29  # Leap year
        elif year % 100 == 0:
            return 28  # Not a leap year
        elif year % 4 == 0:
            return 29  # Leap year
        else:
            return 28  # Not a leap year

def main():
    """Main entry point for command-line usage."""
    parser = argparse.ArgumentParser(description='Batch Security Helper for TNGD')
    parser.add_argument('operation', choices=[
        'create-log-file', 'validate-date', 'sanitize-params', 
        'get-path', 'validate-month-year', 'calculate-days'
    ])
    parser.add_argument('--prefix', default='tngd_backup', help='File prefix for log files')
    parser.add_argument('--suffix', default='.log', help='File suffix for log files')
    parser.add_argument('--date', help='Date to validate (YYYY-MM-DD)')
    parser.add_argument('--params', nargs='*', help='Parameters to sanitize')
    parser.add_argument('--path-type', help='Type of path to get')
    parser.add_argument('--default', default='', help='Default value')
    parser.add_argument('--month', help='Month name or number')
    parser.add_argument('--year', help='Year number')
    
    args = parser.parse_args()
    helper = BatchSecurityHelper()
    
    try:
        if args.operation == 'create-log-file':
            log_file = helper.create_secure_log_file(args.prefix, args.suffix)
            print(log_file)
            
        elif args.operation == 'validate-date':
            if not args.date:
                print("ERROR: --date parameter required", file=sys.stderr)
                sys.exit(1)
            is_valid, error_msg = helper.validate_date_parameter(args.date)
            if is_valid:
                print("VALID")
                sys.exit(0)
            else:
                print(f"INVALID: {error_msg}", file=sys.stderr)
                sys.exit(1)
                
        elif args.operation == 'sanitize-params':
            if not args.params:
                print("")  # Empty result for no params
                sys.exit(0)
            sanitized = helper.sanitize_parameters(args.params)
            print(" ".join(sanitized))
            
        elif args.operation == 'get-path':
            if not args.path_type:
                print("ERROR: --path-type parameter required", file=sys.stderr)
                sys.exit(1)
            path = helper.get_configured_path(args.path_type, args.default)
            print(path)
            
        elif args.operation == 'validate-month-year':
            if not args.month or not args.year:
                print("ERROR: --month and --year parameters required", file=sys.stderr)
                sys.exit(1)
            is_valid, error_msg, month_num, year_num = helper.validate_month_year(args.month, args.year)
            if is_valid:
                print(f"VALID {month_num} {year_num}")
                sys.exit(0)
            else:
                print(f"INVALID: {error_msg}", file=sys.stderr)
                sys.exit(1)
                
        elif args.operation == 'calculate-days':
            if not args.month or not args.year:
                print("ERROR: --month and --year parameters required", file=sys.stderr)
                sys.exit(1)
            try:
                month_num = int(args.month)
                year_num = int(args.year)
                days = helper.calculate_leap_year_days(month_num, year_num)
                print(days)
            except ValueError:
                print("ERROR: Invalid month or year format", file=sys.stderr)
                sys.exit(1)
                
    except Exception as e:
        logger.error(f"Security helper operation failed: {e}")
        print(f"ERROR: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == '__main__':
    main()
