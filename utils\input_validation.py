#!/usr/bin/env python3
"""
Input Validation Module

This module provides functions for validating different types of user inputs.
It helps ensure that inputs are safe, valid, and within expected ranges.
"""

import os
import re
import logging
import datetime
from typing import Any, List, Tuple, Optional, Union

# Configure logging
logger = logging.getLogger(__name__)

def validate_date(date_str: str, format_str: str = '%Y-%m-%d') -> Tuple[bool, Optional[datetime.datetime], str]:
    """
    Validate a date string against the specified format.

    Args:
        date_str: Date string to validate
        format_str: Expected date format (default: YYYY-MM-DD)

    Returns:
        Tuple of (is_valid, parsed_date, error_message)
    """
    if not date_str:
        return False, None, "Date string cannot be empty"

    try:
        parsed_date = datetime.datetime.strptime(date_str, format_str)
        return True, parsed_date, ""
    except ValueError as e:
        error_msg = f"Invalid date format: {date_str}. Expected format: {format_str}"
        logger.warning(error_msg)
        return False, None, error_msg

def validate_numeric_range(value: Any, min_value: Optional[int] = None, 
                          max_value: Optional[int] = None, 
                          param_name: str = "Parameter") -> Tuple[bool, Optional[int], str]:
    """
    Validate that a numeric value is within the specified range.

    Args:
        value: Value to validate
        min_value: Minimum allowed value (inclusive, optional)
        max_value: Maximum allowed value (inclusive, optional)
        param_name: Name of the parameter for error messages

    Returns:
        Tuple of (is_valid, parsed_value, error_message)
    """
    # Check if value is None
    if value is None:
        return False, None, f"{param_name} cannot be None"

    # Try to convert to int
    try:
        int_value = int(value)
    except (ValueError, TypeError):
        error_msg = f"{param_name} must be a valid integer, got: {value}"
        logger.warning(error_msg)
        return False, None, error_msg

    # Check minimum value
    if min_value is not None and int_value < min_value:
        error_msg = f"{param_name} must be at least {min_value}, got: {int_value}"
        logger.warning(error_msg)
        return False, int_value, error_msg

    # Check maximum value
    if max_value is not None and int_value > max_value:
        error_msg = f"{param_name} must be at most {max_value}, got: {int_value}"
        logger.warning(error_msg)
        return False, int_value, error_msg

    return True, int_value, ""

def validate_table_name(table_name: str) -> Tuple[bool, str]:
    """
    Validate that a table name follows the expected format.

    Args:
        table_name: Table name to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not table_name:
        return False, "Table name cannot be empty"

    # Check for common table name patterns (e.g., my.app.tngd.tablename)
    # This regex allows alphanumeric characters, dots, and underscores
    pattern = r'^[a-zA-Z0-9_.]+$'
    if not re.match(pattern, table_name):
        error_msg = f"Invalid table name format: {table_name}. Only alphanumeric characters, dots, and underscores are allowed."
        logger.warning(error_msg)
        return False, error_msg

    # Check for at least one dot (indicating a hierarchical name)
    if '.' not in table_name:
        error_msg = f"Invalid table name format: {table_name}. Expected a hierarchical name with at least one dot."
        logger.warning(error_msg)
        return False, error_msg

    return True, ""

def validate_file_path(file_path: str, must_exist: bool = False, 
                      create_dir: bool = False) -> Tuple[bool, str]:
    """
    Validate that a file path is safe and optionally exists.

    Args:
        file_path: File path to validate
        must_exist: Whether the file must already exist
        create_dir: Whether to create the directory if it doesn't exist

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not file_path:
        return False, "File path cannot be empty"

    # Check for directory traversal attempts
    normalized_path = os.path.normpath(file_path)
    if '..' in normalized_path.split(os.path.sep):
        error_msg = f"Invalid file path: {file_path}. Directory traversal is not allowed."
        logger.warning(error_msg)
        return False, error_msg

    # Check if the file exists if required
    if must_exist and not os.path.exists(file_path):
        error_msg = f"File not found: {file_path}"
        logger.warning(error_msg)
        return False, error_msg

    # Create directory if requested
    if create_dir:
        directory = os.path.dirname(file_path)
        if directory and not os.path.exists(directory):
            try:
                os.makedirs(directory, exist_ok=True)
                logger.info(f"Created directory: {directory}")
            except Exception as e:
                error_msg = f"Failed to create directory {directory}: {str(e)}"
                logger.error(error_msg)
                return False, error_msg

    return True, ""

def validate_tables_list(tables: List[str]) -> Tuple[bool, List[str], List[str]]:
    """
    Validate a list of table names.

    Args:
        tables: List of table names to validate

    Returns:
        Tuple of (all_valid, valid_tables, invalid_tables)
    """
    if not tables:
        return False, [], []

    valid_tables = []
    invalid_tables = []

    for table in tables:
        is_valid, _ = validate_table_name(table)
        if is_valid:
            valid_tables.append(table)
        else:
            invalid_tables.append(table)

    return len(invalid_tables) == 0, valid_tables, invalid_tables
