#!/usr/bin/env python3
"""
Table Checker UI

This script provides a user interface for testing and managing Devo tables.
It allows users to check table existence, view table metadata, and validate tables before backup.
"""

import os
import sys
import json
import logging
import argparse
import datetime
from typing import List, Dict, Any, Optional, Tuple

# Add parent directory to path to allow importing modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Import project modules
from core.devo_client import DevoClient
from core.config_manager import ConfigManager
from tabletest.table_operations import (
    check_table_exists,
    get_table_metadata,
    get_table_sample,
    validate_table_for_backup,
    add_table_to_list,
    remove_table_from_list,
    get_all_tables
)
from utils.logging_utils import init_enhanced_logging

# Configure logging
logger = logging.getLogger(__name__)

def display_header():
    """Display the application header."""
    print("\n" + "=" * 80)
    print("DEVO TABLE CHECKER".center(80))
    print("=" * 80)
    print("This utility helps you test and manage Devo tables for backup.")
    print("=" * 80 + "\n")

def display_menu():
    """Display the main menu options."""
    print("\nMAIN MENU:")
    print("1. Check if a table exists")
    print("2. View table metadata")
    print("3. Get table sample data")
    print("4. Validate table for backup")
    print("5. Add table to backup list")
    print("6. Remove table from backup list")
    print("7. View current backup table list")
    print("8. Check all tables in backup list")
    print("9. Exit")
    return input("\nEnter your choice (1-9): ")

def check_table_existence():
    """Check if a table exists in Devo."""
    table_name = input("\nEnter table name to check: ")
    print(f"\nChecking if table '{table_name}' exists...")

    exists = check_table_exists(table_name)

    if exists:
        print(f"\n✅ Table '{table_name}' exists in Devo.")
    else:
        print(f"\n❌ Table '{table_name}' does not exist in Devo.")

def view_table_metadata():
    """View metadata for a specific table."""
    table_name = input("\nEnter table name: ")
    print(f"\nRetrieving metadata for table '{table_name}'...")

    metadata = get_table_metadata(table_name)

    if metadata:
        print(f"\n📊 Metadata for table '{table_name}':")
        print(f"  - Column count: {len(metadata['columns'])}")
        print(f"  - Columns: {', '.join(metadata['columns'])}")
        if 'row_count' in metadata:
            print(f"  - Estimated row count: {metadata['row_count']:,}")
        if 'size_estimate' in metadata:
            print(f"  - Estimated size: {metadata['size_estimate']}")
    else:
        print(f"\n❌ Could not retrieve metadata for table '{table_name}'.")

def get_sample_data():
    """Get sample data from a table."""
    table_name = input("\nEnter table name: ")
    sample_size = input("Enter sample size (default: 5): ")

    try:
        sample_size = int(sample_size) if sample_size else 5
    except ValueError:
        sample_size = 5
        print("Invalid sample size, using default (5).")

    print(f"\nRetrieving {sample_size} sample rows from table '{table_name}'...")

    sample = get_table_sample(table_name, sample_size)

    if sample:
        print(f"\n📋 Sample data from table '{table_name}':")
        for i, row in enumerate(sample, 1):
            print(f"\nRow {i}:")
            for key, value in row.items():
                print(f"  {key}: {value}")
    else:
        print(f"\n❌ Could not retrieve sample data from table '{table_name}'.")

def validate_table():
    """Validate a table for backup."""
    table_name = input("\nEnter table name to validate: ")
    print(f"\nValidating table '{table_name}' for backup...")

    validation_result = validate_table_for_backup(table_name)

    if validation_result['valid']:
        print(f"\n✅ Table '{table_name}' is valid for backup:")
        print(f"  - Accessible: {validation_result['accessible']}")
        print(f"  - Has data: {validation_result['has_data']}")
        print(f"  - Column count: {len(validation_result['columns'])}")
    else:
        print(f"\n❌ Table '{table_name}' is not valid for backup:")
        print(f"  - Accessible: {validation_result['accessible']}")
        print(f"  - Has data: {validation_result['has_data']}")
        for error in validation_result['errors']:
            print(f"  - Error: {error}")

def add_table():
    """Add a table to the backup list."""
    table_name = input("\nEnter table name to add: ")

    # First check if the table exists
    exists = check_table_exists(table_name)

    if not exists:
        print(f"\n❌ Table '{table_name}' does not exist in Devo. Cannot add to backup list.")
        confirm = input("Add anyway? (y/n): ")
        if confirm.lower() != 'y':
            return

    result = add_table_to_list(table_name)

    if result['success']:
        print(f"\n✅ Table '{table_name}' added to backup list.")
    else:
        print(f"\n❌ Could not add table '{table_name}' to backup list: {result['error']}")

def remove_table():
    """Remove a table from the backup list."""
    table_name = input("\nEnter table name to remove: ")

    result = remove_table_from_list(table_name)

    if result['success']:
        print(f"\n✅ Table '{table_name}' removed from backup list.")
    else:
        print(f"\n❌ Could not remove table '{table_name}' from backup list: {result['error']}")

def view_table_list():
    """View the current list of tables for backup."""
    config = ConfigManager()
    tables = config.get_tables_from_file()

    if tables:
        print(f"\n📋 Current backup table list ({len(tables)} tables):")
        for i, table in enumerate(tables, 1):
            print(f"  {i}. {table}")
    else:
        print("\n❌ No tables in the backup list or could not read the list.")

def check_all_tables():
    """Check all tables in the backup list."""
    config = ConfigManager()
    tables = config.get_tables_from_file()

    if not tables:
        print("\n❌ No tables in the backup list or could not read the list.")
        return

    print(f"\n🔍 Checking {len(tables)} tables in the backup list...")

    results = {
        'total': len(tables),
        'exists': 0,
        'not_exists': 0,
        'valid': 0,
        'invalid': 0,
        'details': {}
    }

    for i, table in enumerate(tables, 1):
        print(f"\nChecking table {i}/{len(tables)}: {table}")

        # Check if the table exists
        exists = check_table_exists(table)

        if exists:
            results['exists'] += 1
            print(f"  ✅ Table exists")

            # Validate the table
            validation = validate_table_for_backup(table)

            if validation['valid']:
                results['valid'] += 1
                print(f"  ✅ Table is valid for backup")
            else:
                results['invalid'] += 1
                print(f"  ❌ Table is not valid for backup")
                for error in validation['errors']:
                    print(f"    - {error}")

            results['details'][table] = {
                'exists': True,
                'valid': validation['valid'],
                'errors': validation['errors'] if not validation['valid'] else []
            }
        else:
            results['not_exists'] += 1
            print(f"  ❌ Table does not exist")

            results['details'][table] = {
                'exists': False,
                'valid': False,
                'errors': ['Table does not exist']
            }

    # Print summary
    print("\n" + "=" * 80)
    print("TABLE CHECK SUMMARY".center(80))
    print("=" * 80)
    print(f"Total tables checked: {results['total']}")
    print(f"Tables that exist: {results['exists']}")
    print(f"Tables that don't exist: {results['not_exists']}")
    print(f"Valid tables: {results['valid']}")
    print(f"Invalid tables: {results['invalid']}")
    print("=" * 80)

def main():
    """Main function for the Table Checker UI."""
    # Initialize logging
    init_enhanced_logging()

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Devo Table Checker UI')
    parser.add_argument('--check', type=str, help='Check if a specific table exists')
    parser.add_argument('--validate', type=str, help='Validate a specific table for backup')
    parser.add_argument('--add', type=str, help='Add a table to the backup list')
    parser.add_argument('--remove', type=str, help='Remove a table from the backup list')
    parser.add_argument('--list', action='store_true', help='List all tables in the backup list')
    parser.add_argument('--check-all', action='store_true', help='Check all tables in the backup list')

    args = parser.parse_args()

    # Handle command line arguments if provided
    if args.check:
        exists = check_table_exists(args.check)
        print(f"Table '{args.check}' {'exists' if exists else 'does not exist'} in Devo.")
        return 0
    elif args.validate:
        validation = validate_table_for_backup(args.validate)
        if validation['valid']:
            print(f"Table '{args.validate}' is valid for backup.")
        else:
            print(f"Table '{args.validate}' is not valid for backup:")
            for error in validation['errors']:
                print(f"  - {error}")
        return 0
    elif args.add:
        result = add_table_to_list(args.add)
        if result['success']:
            print(f"Table '{args.add}' added to backup list.")
        else:
            print(f"Could not add table '{args.add}' to backup list: {result['error']}")
        return 0
    elif args.remove:
        result = remove_table_from_list(args.remove)
        if result['success']:
            print(f"Table '{args.remove}' removed from backup list.")
        else:
            print(f"Could not remove table '{args.remove}' from backup list: {result['error']}")
        return 0
    elif args.list:
        config = ConfigManager()
        tables = config.get_tables_from_file()
        if tables:
            print(f"Backup table list ({len(tables)} tables):")
            for i, table in enumerate(tables, 1):
                print(f"{i}. {table}")
        else:
            print("No tables in the backup list or could not read the list.")
        return 0
    elif args.check_all:
        check_all_tables()
        return 0

    # Interactive mode
    display_header()

    while True:
        choice = display_menu()

        if choice == '1':
            check_table_existence()
        elif choice == '2':
            view_table_metadata()
        elif choice == '3':
            get_sample_data()
        elif choice == '4':
            validate_table()
        elif choice == '5':
            add_table()
        elif choice == '6':
            remove_table()
        elif choice == '7':
            view_table_list()
        elif choice == '8':
            check_all_tables()
        elif choice == '9':
            print("\nExiting Table Checker. Goodbye!")
            break
        else:
            print("\nInvalid choice. Please enter a number between 1 and 9.")

    return 0

if __name__ == "__main__":
    sys.exit(main())
