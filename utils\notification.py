#!/usr/bin/env python3
"""
Notification Module

This module provides functionality for sending email notifications about backup results.
"""

import os
import logging
import smtplib
import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, Any

# Import configuration manager
from core.config_manager import ConfigManager

# Configure logging
logger = logging.getLogger(__name__)

def send_notification(subject: str, message: str, config_manager: ConfigManager = None) -> bool:
    """
    Send an email notification.

    Args:
        subject: Email subject
        message: Email message body
        config_manager: Configuration manager instance (optional)

    Returns:
        True if the email was sent successfully, False otherwise
    """
    # Get configuration
    config = config_manager or ConfigManager()
    email_settings = config.get_email_settings()

    # Check if email settings are configured
    required_settings = [
        'smtp_server',
        'smtp_port',
        'smtp_username',
        'smtp_password',
        'email_from',
        'email_to'
    ]

    missing_settings = [setting for setting in required_settings
                       if not email_settings.get(setting)]

    if missing_settings:
        logger.warning(f"Email settings not fully configured. Missing: {', '.join(missing_settings)}")
        logger.warning("Notification will not be sent.")
        return False

    try:
        # Create the email message
        msg = MIMEMultipart()
        msg['From'] = email_settings['email_from']
        msg['To'] = email_settings['email_to']
        msg['Subject'] = subject

        # Add the message body
        msg.attach(MIMEText(message, 'plain'))

        # Check if we should use mock mode or real email sending
        use_mock_mode = email_settings.get('mock_mode', True)

        if use_mock_mode:
            # MOCK EMAIL SENDING FOR TESTING
            print("=" * 50)
            print("MOCK EMAIL NOTIFICATION")
            print("=" * 50)
            print(f"From: {email_settings['email_from']}")
            print(f"To: {email_settings['email_to']}")
            print(f"Subject: {subject}")
            print("-" * 50)
            print("Message:")
            # Print the first few lines of the message
            message_lines = message.split('\n')
            for line in message_lines[:20]:
                print(line)
            if len(message_lines) > 20:
                print("... (message truncated)")
            print("=" * 50)

            # Save the email to a file for inspection
            email_log_dir = os.path.join('logs', 'emails')
            os.makedirs(email_log_dir, exist_ok=True)
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            email_log_file = os.path.join(email_log_dir, f'email_{timestamp}.txt')

            try:
                with open(email_log_file, 'w', encoding='utf-8') as f:
                    f.write(f"From: {email_settings['email_from']}\n")
                    f.write(f"To: {email_settings['email_to']}\n")
                    f.write(f"Subject: {subject}\n")
                    f.write("-" * 50 + "\n")
                    f.write("Message:\n")
                    f.write(message)

                logger.info(f"Email content saved to {email_log_file}")
                print(f"\033[92m[✓] Email notification would be sent to {email_settings['email_to']}\033[0m")
                print(f"\033[92m[✓] Email content saved to {email_log_file}\033[0m")
            except Exception as e:
                logger.error(f"Error saving email content to file: {str(e)}")
                print(f"\033[93m[!] Could not save email content to file: {str(e)}\033[0m")

            return True

        else:
            # REAL EMAIL SENDING
            try:
                # Connect to the SMTP server
                logger.info(f"Connecting to SMTP server {email_settings['smtp_server']}:{email_settings['smtp_port']}")

                # Use SSL/TLS based on port
                if int(email_settings['smtp_port']) == 465:
                    # SSL connection
                    server = smtplib.SMTP_SSL(email_settings['smtp_server'], int(email_settings['smtp_port']))
                else:
                    # Regular connection with STARTTLS
                    server = smtplib.SMTP(email_settings['smtp_server'], int(email_settings['smtp_port']))
                    server.starttls()

                # Log in to the SMTP server (without logging the password)
                logger.info(f"Logging in as {email_settings['smtp_username']}")
                server.login(email_settings['smtp_username'], email_settings['smtp_password'])

                # Send the email
                logger.info(f"Sending email from {email_settings['email_from']} to {email_settings['email_to']}")
                server.send_message(msg)
                server.quit()

                logger.info(f"Email notification sent successfully to {email_settings['email_to']}")
                print(f"\033[92m[✓] Email notification sent to {email_settings['email_to']}\033[0m")
                return True

            except smtplib.SMTPAuthenticationError as e:
                logger.error(f"SMTP authentication failed: {str(e)}")
                print(f"\033[91m[✗] Email authentication failed. Please check credentials.\033[0m")
                return False
            except smtplib.SMTPConnectError as e:
                logger.error(f"SMTP connection failed: {str(e)}")
                print(f"\033[91m[✗] Could not connect to email server. Please check server settings.\033[0m")
                return False
            except smtplib.SMTPException as e:
                logger.error(f"SMTP error occurred: {str(e)}")
                print(f"\033[91m[✗] Email sending failed: {str(e)}\033[0m")
                return False

    except Exception as e:
        logger.error(f"Error sending email notification: {str(e)}")
        return False

def send_backup_summary_notification(summary: Dict[str, Any], config_manager: ConfigManager = None) -> bool:
    """
    Send a notification with backup summary information.
    Always sends email regardless of success or failure.

    Args:
        summary: Backup summary dictionary
        config_manager: Configuration manager instance (optional)

    Returns:
        True if the notification was sent successfully, False otherwise
    """
    # Get configuration
    config = config_manager or ConfigManager()

    # Check if we should send notifications on completion
    if not config.get('notification', 'send_on_completion', True, value_type=bool):
        logger.info("Notification on completion is disabled in config")
        return False

    # Extract information from the summary
    start_time = summary.get('start_time', '')
    end_time = summary.get('end_time', '')
    duration = summary.get('duration_seconds', 0)
    total_tables = summary.get('total_tables', 0)
    successful_tables = summary.get('successful_tables', 0)
    failed_tables = summary.get('failed_tables', 0)
    total_rows = summary.get('total_rows_backed_up', 0)

    # Determine backup status for subject line
    status = "SUCCESS" if failed_tables == 0 else "PARTIAL SUCCESS" if successful_tables > 0 else "FAILED"

    # Format the date and time strings
    try:
        # Parse ISO format datetime strings
        if 'T' in str(start_time):
            start_dt = datetime.datetime.fromisoformat(start_time)
            start_time_str = start_dt.strftime('%Y-%m-%d %H:%M:%S')
        else:
            start_time_str = str(start_time)

        if 'T' in str(end_time):
            end_dt = datetime.datetime.fromisoformat(end_time)
            date_str = end_dt.strftime('%Y-%m-%d')
            end_time_str = end_dt.strftime('%H:%M:%S')
        else:
            date_str = 'Unknown Date'
            end_time_str = str(end_time)
    except Exception as e:
        logger.error(f"Error parsing date/time: {str(e)}")
        date_str = 'Unknown Date'
        start_time_str = str(start_time)
        end_time_str = str(end_time)

    subject = f"Devo Backup [{status}]: {successful_tables}/{total_tables} tables - {date_str}"

    # Format the message header with status indicator
    if failed_tables == 0:
        header = "✅ BACKUP COMPLETED SUCCESSFULLY"
    elif successful_tables > 0:
        header = "⚠️ BACKUP COMPLETED WITH SOME FAILURES"
    else:
        header = "❌ BACKUP FAILED"

    # Format the message
    # Format duration in a more readable way
    hours, remainder = divmod(duration, 3600)
    minutes, seconds = divmod(remainder, 60)
    if hours > 0:
        duration_str = f"{int(hours)}h {int(minutes)}m {seconds:.2f}s"
    elif minutes > 0:
        duration_str = f"{int(minutes)}m {seconds:.2f}s"
    else:
        duration_str = f"{seconds:.2f}s"

    message = f"""
{header}
==================================================

📊 BACKUP SUMMARY
--------------------------------------------------
Date: {date_str}
Time: {start_time_str} to {end_time_str}
Duration: {duration_str} ({duration:.2f} seconds)

📈 RESULTS
--------------------------------------------------
Total tables: {total_tables}
Successful tables: {successful_tables}
Failed tables: {failed_tables}
Total rows backed up: {total_rows:,}

"""

    # Add performance metrics if available
    performance = summary.get('performance', {})
    if performance:
        resource_metrics = performance.get('resource_metrics', {})
        if resource_metrics:
            message += """
🖥️ PERFORMANCE METRICS
--------------------------------------------------
"""
            # Add CPU metrics
            cpu_percent = resource_metrics.get('cpu_percent', {})
            if cpu_percent:
                message += f"CPU Usage: Avg {cpu_percent.get('mean', 0):.1f}%, Max {cpu_percent.get('max', 0):.1f}%\n"

            # Add memory metrics
            memory_percent = resource_metrics.get('memory_percent', {})
            if memory_percent:
                message += f"Memory Usage: Avg {memory_percent.get('mean', 0):.1f}%, Max {memory_percent.get('max', 0):.1f}%\n"

            # Add disk metrics
            disk_percent = resource_metrics.get('disk_percent', {})
            if disk_percent:
                message += f"Disk Usage: Avg {disk_percent.get('mean', 0):.1f}%, Max {disk_percent.get('max', 0):.1f}%\n"

    # Add details about successful tables (top 10 by row count)
    results = summary.get('results', {})
    successful_tables_data = []

    for table_name, result in results.items():
        if result.get('status') == 'success' and result.get('rows_backed_up', 0) > 0:
            successful_tables_data.append({
                'name': table_name,
                'rows': result.get('rows_backed_up', 0),
                'size': result.get('compressed_size_bytes', 0),
                'time': result.get('total_time_seconds', 0)
            })

    if successful_tables_data:
        # Sort by row count (descending)
        successful_tables_data.sort(key=lambda x: x['rows'], reverse=True)

        message += """
✅ TOP SUCCESSFUL TABLES (by row count)
--------------------------------------------------
"""
        # Show top 10 tables
        for i, table in enumerate(successful_tables_data[:10]):
            size_mb = table['size'] / (1024 * 1024)
            message += f"{i+1}. {table['name']}\n"
            message += f"   Rows: {table['rows']:,}, Size: {size_mb:.2f} MB, Time: {table['time']:.2f} sec\n"

        # Add note if there are more tables
        if len(successful_tables_data) > 10:
            message += f"\n... and {len(successful_tables_data) - 10} more successful tables\n"

    # Add details about failed tables
    include_failed = config.get('notification', 'include_failed_tables', True, value_type=bool)
    if failed_tables > 0 and include_failed:
        message += """
❌ FAILED TABLES
--------------------------------------------------
"""
        for table_name, result in results.items():
            if result.get('status') == 'error':
                error = result.get('error', 'Unknown error')
                error_type = result.get('error_type', 'Unknown')

                # Extract more detailed information if available
                details = result.get('details', {})
                context = details.get('context', '') if isinstance(details, dict) else ''

                # Format the error message with more details
                message += f"- {table_name}:\n"
                message += f"  Error Type: {error_type}\n"
                message += f"  Context: {context}\n"
                message += f"  Message: {error}\n"

                # Add troubleshooting hint based on error type
                if "Query parsing error" in error:
                    message += f"  Hint: Table has columns with special characters that need escaping. Will be fixed in next run with the updated code.\n"
                elif "Failed to establish a new connection" in error:
                    message += f"  Hint: Network connectivity issue. Check network connection and try again.\n"

                message += "\n"

    # Add tables with no data if any
    no_data_tables = []
    for table_name, result in results.items():
        if result.get('status') == 'success' and result.get('rows_backed_up', 0) == 0:
            no_data_tables.append(table_name)

    if no_data_tables:
        message += """
ℹ️ TABLES WITH NO DATA
--------------------------------------------------
"""
        for table_name in no_data_tables[:20]:  # Limit to 20 to avoid too long emails
            message += f"- {table_name}\n"

        if len(no_data_tables) > 20:
            message += f"\n... and {len(no_data_tables) - 20} more tables with no data\n"

    # Add footer with links to logs
    message += """
--------------------------------------------------
This is an automated message from the Devo Backup System.
For detailed logs, please check the logs directory on the backup server.
"""

    # Send the notification
    return send_notification(subject, message, config_manager)

def send_backup_error_notification(error_message: str, config_manager: ConfigManager = None) -> bool:
    """
    Send a notification about a backup error.

    Args:
        error_message: Error message
        config_manager: Configuration manager instance (optional)

    Returns:
        True if the notification was sent successfully, False otherwise
    """
    # Get configuration
    config = config_manager or ConfigManager()

    # Check if we should send notifications on error
    if not config.get('notification', 'send_on_error', True, value_type=bool):
        logger.info("Notification on error is disabled in config")
        return False

    # Format the subject
    subject = f"Devo Backup Error: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

    # Format the message
    message = f"""
Devo Tables Backup Error
=======================

Time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Error:
{error_message}

Please check the logs for more details.
"""

    # Send the notification
    return send_notification(subject, message, config_manager)

def send_validation_notification(validation_results: Dict[str, Any], config_manager: ConfigManager = None) -> bool:
    """
    Send a notification with backup validation results.

    Args:
        validation_results: Validation results dictionary
        config_manager: Configuration manager instance (optional)

    Returns:
        True if the notification was sent successfully, False otherwise
    """
    # Extract information from the validation results
    total_tables = validation_results.get('total_tables', 0)
    found_tables = validation_results.get('found_tables', 0)
    missing_tables = validation_results.get('missing_tables', [])
    errors = validation_results.get('errors', [])

    # Format the subject
    subject = f"Devo Backup Validation: {found_tables}/{total_tables} tables verified"

    # Format the message
    message = f"""
Devo Tables Backup Validation
============================

Time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Results:
- Total tables: {total_tables}
- Found tables: {found_tables}
- Missing tables: {len(missing_tables)}

"""

    # Add details about missing tables
    if missing_tables:
        message += "\nMissing Tables:\n"
        for table_name in missing_tables:
            message += f"- {table_name}\n"

    # Add details about errors
    if errors:
        message += "\nErrors:\n"
        for error in errors:
            message += f"- {error}\n"

    # Send the notification
    return send_notification(subject, message, config_manager)
