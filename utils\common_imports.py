#!/usr/bin/env python3
"""
Common Import Utilities

This module provides common import patterns and path setup utilities
to eliminate code duplication across the TNGD backup system.

CONSOLIDATION FIX: Eliminates duplicate path setup and import patterns
found across multiple modules.
"""

import os
import sys
from pathlib import Path
from typing import Optional


def setup_project_path(file_path: str = None) -> str:
    """
    Set up the project root path for imports.
    
    This function eliminates the duplicate path setup code found in:
    - monthly_backup_strategy.py
    - daily_backup_scheduler.py  
    - backup_single_table.py
    - And other modules
    
    Args:
        file_path: The __file__ path of the calling module (optional)
        
    Returns:
        The project root path as a string
    """
    if file_path:
        # Calculate project root relative to the calling file
        current_file = Path(file_path)
        if 'scripts' in current_file.parts:
            # For files in scripts directory
            project_root = current_file.parent.parent
        elif 'utils' in current_file.parts or 'core' in current_file.parts:
            # For files in utils or core directories
            project_root = current_file.parent.parent
        elif 'tests' in current_file.parts:
            # For test files
            project_root = current_file.parent.parent
        else:
            # For files in project root
            project_root = current_file.parent
    else:
        # Default to current working directory's parent
        project_root = Path.cwd()
    
    project_root_str = str(project_root)
    
    # Add to Python path if not already present
    if project_root_str not in sys.path:
        sys.path.insert(0, project_root_str)
    
    return project_root_str


def get_common_imports():
    """
    Get common import statements used across multiple modules.
    
    Returns:
        Dictionary with common import objects
    """
    # Ensure project path is set up
    setup_project_path()
    
    try:
        from core.backup_config import BackupConfig
        from core.config_manager import ConfigManager
        from core.unified_table_processor import UnifiedTableProcessor
        
        return {
            'BackupConfig': BackupConfig,
            'ConfigManager': ConfigManager,
            'UnifiedTableProcessor': UnifiedTableProcessor
        }
    except ImportError as e:
        # Graceful fallback if imports fail
        print(f"Warning: Could not import common modules: {e}")
        return {}


class ProjectPathManager:
    """
    Context manager for handling project path setup and cleanup.
    
    This eliminates the need for manual path manipulation in each module.
    """
    
    def __init__(self, file_path: str = None):
        """
        Initialize the path manager.
        
        Args:
            file_path: The __file__ path of the calling module
        """
        self.file_path = file_path
        self.original_path = sys.path.copy()
        self.project_root = None
    
    def __enter__(self):
        """Set up the project path."""
        self.project_root = setup_project_path(self.file_path)
        return self.project_root
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Restore the original path."""
        sys.path[:] = self.original_path


# Convenience function for common usage pattern
def with_project_imports(file_path: str):
    """
    Decorator to automatically set up project imports for a module.
    
    Args:
        file_path: The __file__ path of the calling module
        
    Usage:
        @with_project_imports(__file__)
        def main():
            # Your code here with imports available
            pass
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            with ProjectPathManager(file_path):
                return func(*args, **kwargs)
        return wrapper
    return decorator


# Common configuration patterns
def get_standard_config() -> Optional[object]:
    """
    Get a standard configuration setup used across modules.
    
    Returns:
        ConfigManager instance or None if import fails
    """
    try:
        setup_project_path()
        from core.config_manager import ConfigManager
        return ConfigManager()
    except ImportError:
        return None


def get_standard_backup_config() -> Optional[object]:
    """
    Get a standard backup configuration setup used across modules.
    
    Returns:
        BackupConfig instance or None if import fails
    """
    try:
        setup_project_path()
        from core.backup_config import BackupConfig
        return BackupConfig()
    except ImportError:
        return None


# Export commonly used functions
__all__ = [
    'setup_project_path',
    'get_common_imports', 
    'ProjectPathManager',
    'with_project_imports',
    'get_standard_config',
    'get_standard_backup_config'
]
