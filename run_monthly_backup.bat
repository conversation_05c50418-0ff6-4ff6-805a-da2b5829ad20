@echo off
setlocal enabledelayedexpansion
REM ===============================================================
REM TNGD Monthly Backup System                             S4NG-7
REM ===============================================================
REM Unified monthly backup system with integrated functionality:
REM - Monthly backup operations (by month/year)
REM - Historical backup operations (by date range)
REM - Multiple backup strategies for maximum reliability
REM
REM This script runs complete monthly or historical backups using
REM the existing daily backup system. It processes data
REM sequentially for maximum reliability.
REM
REM Usage:
REM   run_monthly_backup.bat [mode] [parameters] [options]
REM
REM Modes:
REM   monthly            Monthly backup by month/year (default)
REM   historical         Historical backup by date range
REM   help               Show this help message
REM
REM Monthly Mode Examples:
REM   run_monthly_backup.bat march 2025
REM   run_monthly_backup.bat 3 2025
REM   run_monthly_backup.bat monthly march 2024 --dry-run
REM   run_monthly_backup.bat 3 2025 --start-day 15
REM
REM Historical Mode Examples:
REM   run_monthly_backup.bat historical 2025-03-01 2025-03-31
REM   run_monthly_backup.bat historical 2025-03-15 2025-03-15 --dry-run
REM   run_monthly_backup.bat historical 2025-03-01 2025-03-05 --verbose
REM
REM Monthly Options:
REM   --start-day DD     Start from specific day (default: 1)
REM   --end-day DD       End at specific day (default: last day of month)
REM   --dry-run          Validate only, no backup
REM   --verbose          Enable verbose logging
REM
REM Historical Options:
REM   --dry-run          Validate only, no backup
REM   --verbose          Enable verbose logging
REM   --single-table     Test with single table only
REM ===============================================================

REM Set the current directory to the script directory
cd /d "%~dp0"

REM Set default values
set MODE=monthly
set MONTH_NAME=%1
set YEAR=%2
set START_DAY=1
set END_DAY=
set DRY_RUN=false
set VERBOSE=false
set SINGLE_TABLE=false
set PYTHON_CMD=python
set HISTORICAL_BACKUP_SCRIPT=scripts\historical_backup_processor.py

REM Process command line arguments and determine mode
if "%1"=="" goto help
if "%1"=="help" goto help
if "%1"=="--help" goto help

REM Check if first argument is a mode
if "%1"=="monthly" (
    set MODE=monthly
    set MONTH_NAME=%2
    set YEAR=%3
    shift
    shift
    goto monthly_mode
)
if "%1"=="historical" (
    set MODE=historical
    set START_DATE=%2
    set END_DATE=%3
    shift
    shift
    goto historical_mode
)

REM If first argument looks like a date (YYYY-MM-DD), treat as historical mode
echo %1 | findstr /r "^[0-9][0-9][0-9][0-9]-[0-9][0-9]-[0-9][0-9]$" >nul
if %ERRORLEVEL% EQU 0 (
    set MODE=historical
    set START_DATE=%1
    set END_DATE=%2
    shift
    goto historical_mode
)

REM Default to monthly mode
set MODE=monthly
goto monthly_mode

:monthly_mode
REM Convert month name to number if needed
if "%MONTH_NAME%"=="january" set MONTH_NUM=1
if "%MONTH_NAME%"=="february" set MONTH_NUM=2
if "%MONTH_NAME%"=="march" set MONTH_NUM=3
if "%MONTH_NAME%"=="april" set MONTH_NUM=4
if "%MONTH_NAME%"=="may" set MONTH_NUM=5
if "%MONTH_NAME%"=="june" set MONTH_NUM=6
if "%MONTH_NAME%"=="july" set MONTH_NUM=7
if "%MONTH_NAME%"=="august" set MONTH_NUM=8
if "%MONTH_NAME%"=="september" set MONTH_NUM=9
if "%MONTH_NAME%"=="october" set MONTH_NUM=10
if "%MONTH_NAME%"=="november" set MONTH_NUM=11
if "%MONTH_NAME%"=="december" set MONTH_NUM=12

REM If not a month name, assume it's a number
if "%MONTH_NUM%"=="" set MONTH_NUM=%MONTH_NAME%

REM Validate month number
if %MONTH_NUM% LSS 1 goto invalid_month
if %MONTH_NUM% GTR 12 goto invalid_month

REM Set default year if not provided
if "%YEAR%"=="" set YEAR=2025

REM Parse additional options
shift
shift
:parse_options
if "%1"=="" goto options_done
if "%1"=="--start-day" (
    set START_DAY=%2
    shift & shift & goto parse_options
)
if "%1"=="--end-day" (
    set END_DAY=%2
    shift & shift & goto parse_options
)
if "%1"=="--dry-run" (
    set DRY_RUN=true
    shift & goto parse_options
)
if "%1"=="--verbose" (
    set VERBOSE=true
    shift & goto parse_options
)
shift & goto parse_options

:options_done

REM Determine end day if not specified
if "%END_DAY%"=="" (
    if %MONTH_NUM%==1 set END_DAY=31
    if %MONTH_NUM%==2 set END_DAY=28
    if %MONTH_NUM%==3 set END_DAY=31
    if %MONTH_NUM%==4 set END_DAY=30
    if %MONTH_NUM%==5 set END_DAY=31
    if %MONTH_NUM%==6 set END_DAY=30
    if %MONTH_NUM%==7 set END_DAY=31
    if %MONTH_NUM%==8 set END_DAY=31
    if %MONTH_NUM%==9 set END_DAY=30
    if %MONTH_NUM%==10 set END_DAY=31
    if %MONTH_NUM%==11 set END_DAY=30
    if %MONTH_NUM%==12 set END_DAY=31

    REM Check for leap year (simplified)
    if %MONTH_NUM%==2 (
        set /a leap_check=%YEAR% %% 4
        if !leap_check!==0 set END_DAY=29
    )
)

echo ===============================================================
echo TNGD MONTHLY BACKUP SYSTEM
echo ===============================================================
echo Target: Month %MONTH_NUM% (%MONTH_NAME%) %YEAR%
echo Days: %START_DAY% to %END_DAY%
if "%DRY_RUN%"=="true" echo Mode: DRY RUN (validation only)
echo Current time: %date% %time%
echo ===============================================================
echo.

REM Check if tables.json exists
if not exist tabletest\tables.json (
    echo ERROR: tabletest\tables.json not found!
    echo The backup cannot proceed without the table list.
    exit /b 1
)

REM Create monthly log directory
set LOG_DIR=logs\monthly\%YEAR%-%MONTH_NUM:~-2%
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

REM Initialize counters
set SUCCESSFUL_DAYS=0
set FAILED_DAYS=0
set TOTAL_DAYS=0

echo Starting monthly backup process...
echo Processing days %START_DAY% to %END_DAY%...
echo.

REM Process each day
for /l %%d in (%START_DAY%,1,%END_DAY%) do (
    set /a TOTAL_DAYS+=1
    set DAY=%%d
    if !DAY! LSS 10 set DAY=0!DAY!

    echo ===============================================================
    echo Processing Day %%d of Month %MONTH_NUM% (%YEAR%-%MONTH_NUM%-!DAY!)
    echo ===============================================================

    REM Build the specific date
    set TARGET_DATE=%YEAR%-%MONTH_NUM%-!DAY!

    REM Build command parameters
    set DAILY_PARAMS=
    if "%DRY_RUN%"=="true" set DAILY_PARAMS=!DAILY_PARAMS! --dry-run
    if "%VERBOSE%"=="true" set DAILY_PARAMS=!DAILY_PARAMS! --verbose

    REM Run daily backup for this specific day
    echo Running: run_daily_backup.bat !DAILY_PARAMS!
    echo Target date: !TARGET_DATE!
    echo.

    call run_daily_backup.bat !DAILY_PARAMS!
    set DAY_EXIT_CODE=!ERRORLEVEL!

    if !DAY_EXIT_CODE! EQU 0 (
        echo ✅ Day %%d completed successfully
        set /a SUCCESSFUL_DAYS+=1
    ) else (
        echo ❌ Day %%d failed with exit code !DAY_EXIT_CODE!
        set /a FAILED_DAYS+=1
    )

    echo.
    echo Progress: !SUCCESSFUL_DAYS! successful, !FAILED_DAYS! failed out of !TOTAL_DAYS! processed
    echo.

    REM For dry-run, just process the first day to test
    if "%DRY_RUN%"=="true" (
        echo.
        echo DRY-RUN: Processing only first day for validation
        goto end_processing
    )

    REM Small delay between days
    timeout /t 2 /nobreak >nul
)

:end_processing

echo ===============================================================
echo MONTHLY BACKUP COMPLETED
echo ===============================================================
echo Target: Month %MONTH_NUM% (%MONTH_NAME%) %YEAR%
echo Total days processed: %TOTAL_DAYS%
echo Successful days: %SUCCESSFUL_DAYS%
echo Failed days: %FAILED_DAYS%
echo Completion time: %date% %time%
echo ===============================================================

if %FAILED_DAYS% EQU 0 (
    echo ✅ Monthly backup completed successfully!
    echo All days were processed without errors.
    exit /b 0
) else (
    echo ⚠️ Monthly backup completed with %FAILED_DAYS% failed days.
    echo Check individual day logs for details.
    exit /b 1
)

:invalid_month
echo ERROR: Invalid month '%MONTH_NAME%'. Must be 1-12 or month name.
exit /b 1

:historical_mode
echo ===============================================================
echo TNGD HISTORICAL BACKUP SYSTEM
echo ===============================================================
echo Start Date: %START_DATE%
echo End Date: %END_DATE%
echo Current time: %date% %time%
echo ===============================================================
echo.

REM Validate date format (basic check)
if "%START_DATE%"=="" (
    echo ERROR: Start date is required
    goto help
)

if "%END_DATE%"=="" (
    echo ERROR: End date is required
    goto help
)

REM Parse additional options for historical mode
shift
:parse_historical_options
if "%1"=="" goto historical_options_done
if "%1"=="--dry-run" (
    set DRY_RUN=true
    shift & goto parse_historical_options
)
if "%1"=="--verbose" (
    set VERBOSE=true
    shift & goto parse_historical_options
)
if "%1"=="--single-table" (
    set SINGLE_TABLE=true
    shift & goto parse_historical_options
)
shift & goto parse_historical_options

:historical_options_done

REM Create logs directory if it doesn't exist
if not exist logs mkdir logs
if not exist logs\historical mkdir logs\historical

REM Get current date and time for logging
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YYYY=%dt:~0,4%"
set "MM=%dt:~4,2%"
set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%"
set "Min=%dt:~10,2%"
set "Sec=%dt:~12,2%"
set "LOG_FILE=logs\historical\historical_backup_%YYYY%%MM%%DD%_%HH%%Min%%Sec%.log"

if "%DRY_RUN%"=="true" echo Mode: DRY RUN (validation only)
if "%SINGLE_TABLE%"=="true" echo Mode: SINGLE TABLE TEST
echo Log file: %LOG_FILE%
echo.

REM Check if tables.json exists
if not exist tabletest\tables.json (
    echo ERROR: tabletest\tables.json not found!
    echo The backup cannot proceed without the table list.
    exit /b 1
)

REM Check disk space
echo Checking disk space and performing cleanup...
%PYTHON_CMD% utils\disk_cleanup.py --force --verbose
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Insufficient disk space for historical backup.
    exit /b 1
)

REM Build command parameters
set PARAMS=--start-date %START_DATE% --end-date %END_DATE%
if "%DRY_RUN%"=="true" set PARAMS=%PARAMS% --dry-run
if "%VERBOSE%"=="true" set PARAMS=%PARAMS% --verbose
if "%SINGLE_TABLE%"=="true" set PARAMS=%PARAMS% --single-table

REM Run the historical backup processor
echo Starting historical backup process...
echo Command: %PYTHON_CMD% %HISTORICAL_BACKUP_SCRIPT% %PARAMS%
echo.

%PYTHON_CMD% %HISTORICAL_BACKUP_SCRIPT% %PARAMS% > %LOG_FILE% 2>&1
set BACKUP_EXIT_CODE=%ERRORLEVEL%

echo.
echo ===============================================================
echo HISTORICAL BACKUP COMPLETED at %date% %time%
echo Exit code: %BACKUP_EXIT_CODE%
echo ===============================================================

if %BACKUP_EXIT_CODE% EQU 0 (
    echo ✅ Historical backup completed successfully!
) else (
    echo ❌ Historical backup failed or completed with errors.
    echo Check the log file for details: %LOG_FILE%
)

echo.
echo Log file: %LOG_FILE%
echo.

REM Show recent log entries for quick review
echo ===============================================================
echo RECENT LOG ENTRIES (Last 20 lines):
echo ===============================================================
if exist "%LOG_FILE%" (
    powershell -Command "Get-Content '%LOG_FILE%' | Select-Object -Last 20"
) else (
    echo Log file not found.
)
echo ===============================================================

exit /b %BACKUP_EXIT_CODE%

:help
echo ===============================================================
echo TNGD Monthly Backup System - Unified Help
echo ===============================================================
echo.
echo This is the unified monthly backup system that consolidates
echo monthly and historical backup functionality into a single interface.
echo.
echo Usage:
echo   run_monthly_backup.bat [mode] [parameters] [options]
echo.
echo MODES:
echo ===============================================================
echo.
echo 1. MONTHLY MODE (default)
echo   run_monthly_backup.bat [monthly] [month] [year] [options]
echo
echo   Parameters:
echo     month              Month name (january, february, march, etc.) or number (1-12)
echo     year               Year (e.g., 2025)
echo
echo   Options:
echo     --start-day DD     Start from specific day (default: 1)
echo     --end-day DD       End at specific day (default: last day of month)
echo     --dry-run          Validate only, no backup
echo     --verbose          Enable verbose logging
echo.
echo   Examples:
echo     run_monthly_backup.bat march 2025
echo     run_monthly_backup.bat 3 2025
echo     run_monthly_backup.bat monthly march 2024 --dry-run
echo     run_monthly_backup.bat 3 2025 --start-day 15 --end-day 20
echo     run_monthly_backup.bat february 2024 --verbose
echo.
echo 2. HISTORICAL MODE
echo   run_monthly_backup.bat historical [start-date] [end-date] [options]
echo
echo   Parameters:
echo     start-date         Start date in YYYY-MM-DD format
echo     end-date           End date in YYYY-MM-DD format
echo
echo   Options:
echo     --dry-run          Validate only, no backup
echo     --verbose          Enable verbose logging
echo     --single-table     Test with single table only
echo.
echo   Examples:
echo     run_monthly_backup.bat historical 2025-03-01 2025-03-31
echo     run_monthly_backup.bat historical 2025-03-15 2025-03-15 --dry-run
echo     run_monthly_backup.bat historical 2025-03-01 2025-03-05 --verbose
echo     run_monthly_backup.bat 2025-03-01 2025-03-31
echo.
echo ===============================================================
echo FEATURES:
echo ===============================================================
echo   ✅ Day-by-day sequential processing for maximum reliability
echo   ✅ Uses existing daily backup infrastructure
echo   ✅ Comprehensive error handling and retry mechanism
echo   ✅ Progress tracking and email notifications
echo   ✅ Resume capability from last successful day
echo   ✅ Multiple backup strategies available
echo   ✅ Historical date range backup support
echo   ✅ Automatic disk space management
echo   ✅ Configurable OSS path structure: Devo/{month}/week {n}/{date}/
echo.
echo ===============================================================
echo BACKUP STRATEGIES:
echo ===============================================================
echo 1. Day-by-Day Sequential (Recommended)
echo    - Maximum reliability and easy resumption
echo    - Each day processed independently
echo    - Minimal resource usage
echo.
echo 2. Week-by-Week Chunked
echo    - Balance between efficiency and reliability
echo    - Weekly data chunks
echo    - Faster than day-by-day
echo.
echo 3. Hybrid Adaptive
echo    - Adapts to table characteristics
echo    - Optimized chunk sizes
echo    - Best for diverse datasets
echo.
echo 4. Emergency Fallback
echo    - Maximum safety mode
echo    - Conservative settings with maximum retries
echo    - Used when other strategies fail
echo.
echo ===============================================================
echo QUICK START:
echo ===============================================================
echo 1. Test monthly backup:     run_monthly_backup.bat march 2025 --dry-run
echo 2. Run monthly backup:      run_monthly_backup.bat march 2025
echo 3. Test historical backup:  run_monthly_backup.bat historical 2025-03-01 2025-03-31 --dry-run
echo 4. Run historical backup:   run_monthly_backup.bat historical 2025-03-01 2025-03-31
echo.
echo This script uses the existing daily backup system to process
echo data sequentially for maximum reliability. Each day/period is
echo processed as a separate backup operation with full error handling.
echo.
echo ===============================================================
exit /b 0
