#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Disk Cleanup Utility for Devo Backup System

This module provides functions to clean up temporary files and manage disk space
for the Devo backup system. It can be run as a standalone script or imported
and used by other modules.
"""

import os
import sys
import shutil
import logging
import argparse
import datetime
import time
import psutil
from typing import List, Dict, Tuple, Optional

# Add parent directory to path to allow imports when run as script
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.config_manager import ConfigManager

# Configure logging
logger = logging.getLogger(__name__)

class DiskCleanup:
    """Disk cleanup utility for managing temporary files and disk space."""

    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the disk cleanup utility.

        Args:
            config_manager: Optional ConfigManager instance. If not provided, a new one will be created.
        """
        self.config_manager = config_manager or ConfigManager()
        self.temp_dir = os.path.join(os.getcwd(), self.config_manager.get('storage', 'temp_dir', 'temp'))
        self.log_dir = os.path.join(os.getcwd(), self.config_manager.get('logging', 'log_dir', 'logs'))
        self.disk_threshold = self.config_manager.get('performance_monitoring', 'alert_thresholds', {}).get('disk_percent', 65)
        self.emergency_threshold = 75  # Emergency threshold for critical cleanup

        # Status file path for tracking active backup processes
        self.status_file = os.path.join(self.temp_dir, 'backup_status.json')

    def check_disk_space(self) -> Tuple[float, bool]:
        """
        Check available disk space.

        Returns:
            Tuple containing disk usage percentage and boolean indicating if it's above threshold
        """
        disk_usage = psutil.disk_usage(os.getcwd())
        disk_percent = disk_usage.percent
        is_critical = disk_percent >= self.disk_threshold

        logger.info(f"Current disk usage: {disk_percent:.1f}% (threshold: {self.disk_threshold}%)")
        if is_critical:
            logger.warning(f"Disk usage is above threshold: {disk_percent:.1f}% >= {self.disk_threshold}%")

        return disk_percent, is_critical

    def get_active_backup_directories(self) -> List[str]:
        """
        Get a list of directories that are currently being used by active backup processes.

        This method checks the backup status file and running processes to identify
        directories that should not be cleaned up.

        Returns:
            List of directory paths that should be preserved
        """
        active_dirs = []

        # Check if backup status file exists
        if os.path.exists(self.status_file):
            try:
                import json
                with open(self.status_file, 'r') as f:
                    status_data = json.load(f)

                # Check if there's an active backup process
                last_updated = status_data.get('last_updated', 0)
                current_time = time.time()

                # Consider a backup active if the status file was updated in the last hour
                if current_time - last_updated < 3600:  # 1 hour in seconds
                    logger.info("Active backup process detected from status file")

                    # Get the tables being processed
                    tables_processed = status_data.get('tables_processed', 0)
                    tables_total = status_data.get('tables_total', 0)

                    if tables_processed < tables_total:
                        logger.info(f"Backup in progress: {tables_processed}/{tables_total} tables processed")

                        # Mark all temp directories as active to be safe
                        if os.path.exists(self.temp_dir):
                            for item in os.listdir(self.temp_dir):
                                item_path = os.path.join(self.temp_dir, item)
                                if os.path.isdir(item_path):
                                    active_dirs.append(item_path)
                                    logger.debug(f"Marked directory as active: {item_path}")
            except Exception as e:
                logger.warning(f"Error reading backup status file: {str(e)}")

        # Check for running Python processes that might be backup processes
        try:
            import psutil
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    # Look for Python processes running backup scripts
                    if proc.info['name'] == 'python.exe' and proc.info['cmdline']:
                        cmdline = ' '.join(proc.info['cmdline'])
                        if 'backup' in cmdline.lower() and 'main.py' in cmdline:
                            logger.info(f"Active backup process detected: PID {proc.info['pid']}")

                            # Mark all temp directories as active when a backup process is running
                            if os.path.exists(self.temp_dir):
                                for item in os.listdir(self.temp_dir):
                                    item_path = os.path.join(self.temp_dir, item)
                                    if os.path.isdir(item_path) and item_path not in active_dirs:
                                        active_dirs.append(item_path)
                                        logger.debug(f"Marked directory as active due to running process: {item_path}")
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass
        except Exception as e:
            logger.warning(f"Error checking for active processes: {str(e)}")

        # If we found active directories, log them
        if active_dirs:
            logger.info(f"Found {len(active_dirs)} active backup directories that will be preserved")

        return active_dirs

    def cleanup_temp_directory(self, force: bool = False, age_hours: int = 3) -> int:
        """
        Clean up temporary files in the temp directory.

        Args:
            force: If True, remove all files regardless of age
            age_hours: Remove files older than this many hours

        Returns:
            Number of files removed
        """
        if not os.path.exists(self.temp_dir):
            logger.info(f"Temp directory does not exist: {self.temp_dir}")
            return 0

        logger.info(f"Cleaning up temp directory: {self.temp_dir}")

        # Get active backup directories that should be preserved
        active_dirs = self.get_active_backup_directories()

        # Calculate cutoff time
        cutoff_time = time.time() - (age_hours * 60 * 60)
        removed_files = 0
        total_files = 0
        total_size_removed = 0
        preserved_files = 0

        # Walk through temp directory and remove old files
        for root, dirs, files in os.walk(self.temp_dir, topdown=False):
            # Check if this directory should be preserved (it's part of an active backup)
            should_preserve_dir = False
            for active_dir in active_dirs:
                if root == active_dir or root.startswith(active_dir + os.sep):
                    should_preserve_dir = True
                    logger.debug(f"Preserving directory: {root} (active backup)")
                    break

            if should_preserve_dir:
                # Skip this directory and all its files
                preserved_files += len(files)
                logger.debug(f"Preserving {len(files)} files in active backup directory: {root}")
                continue

            for file in files:
                total_files += 1
                file_path = os.path.join(root, file)
                try:
                    # Check if file is older than cutoff time or if force cleanup is enabled
                    file_mod_time = os.path.getmtime(file_path)
                    file_size = os.path.getsize(file_path)

                    if force or file_mod_time < cutoff_time:
                        os.remove(file_path)
                        removed_files += 1
                        total_size_removed += file_size
                        logger.debug(f"Removed file: {file_path}")
                except Exception as e:
                    logger.warning(f"Could not remove file {file_path}: {str(e)}")

            # Try to remove empty directories
            for dir_name in dirs:
                dir_path = os.path.join(root, dir_name)
                # Skip active directories
                should_preserve = False
                for active_dir in active_dirs:
                    if dir_path == active_dir or dir_path.startswith(active_dir + os.sep):
                        should_preserve = True
                        break

                if should_preserve:
                    logger.debug(f"Preserving directory: {dir_path} (active backup)")
                    continue

                try:
                    if not os.listdir(dir_path):  # Only remove if empty
                        os.rmdir(dir_path)
                        logger.debug(f"Removed empty directory: {dir_path}")
                except Exception as e:
                    logger.warning(f"Could not remove directory {dir_path}: {str(e)}")

        logger.info(f"Removed {removed_files} of {total_files} files ({total_size_removed / (1024*1024):.2f} MB) from temp directory")
        if preserved_files > 0:
            logger.info(f"Preserved {preserved_files} files in active backup directories")
        return removed_files

    def cleanup_old_logs(self, max_age_days: int = 7) -> int:
        """
        Clean up old log files.

        Args:
            max_age_days: Remove log files older than this many days

        Returns:
            Number of files removed
        """
        if not os.path.exists(self.log_dir):
            logger.info(f"Log directory does not exist: {self.log_dir}")
            return 0

        logger.info(f"Cleaning up old logs in: {self.log_dir}")

        # Calculate cutoff time
        cutoff_time = time.time() - (max_age_days * 24 * 60 * 60)
        removed_files = 0
        total_files = 0

        # Walk through log directory and remove old files
        for root, _, files in os.walk(self.log_dir):
            for file in files:
                if file.endswith('.log'):
                    total_files += 1
                    file_path = os.path.join(root, file)
                    try:
                        # Check if file is older than cutoff time
                        file_mod_time = os.path.getmtime(file_path)
                        if file_mod_time < cutoff_time:
                            os.remove(file_path)
                            removed_files += 1
                            logger.debug(f"Removed old log file: {file_path}")
                    except Exception as e:
                        logger.warning(f"Could not remove log file {file_path}: {str(e)}")

        logger.info(f"Removed {removed_files} of {total_files} log files")
        return removed_files

    def emergency_cleanup(self) -> bool:
        """
        Perform emergency cleanup when disk space is critically low.

        Returns:
            True if cleanup was successful, False otherwise
        """
        logger.warning("Performing emergency cleanup due to critically low disk space")

        # Get active backup directories that should be preserved
        active_dirs = self.get_active_backup_directories()
        if active_dirs:
            logger.warning(f"Found {len(active_dirs)} active backup directories that will be preserved during emergency cleanup")

        # First try to remove all temp files (the method already respects active directories)
        removed = self.cleanup_temp_directory(force=True, age_hours=0)

        # Check if disk space is still critical
        disk_percent, is_critical = self.check_disk_space()
        if not is_critical:
            logger.info("Emergency cleanup successful")
            return True

        # If still critical, try to remove all compressed files in temp directory
        # but preserve active backup files
        logger.warning("Disk space still critical after initial cleanup, removing non-active compressed files")
        removed_compressed = 0
        preserved_compressed = 0

        if os.path.exists(self.temp_dir):
            for root, _, files in os.walk(self.temp_dir):
                # Check if this directory should be preserved
                should_preserve_dir = False
                for active_dir in active_dirs:
                    if root == active_dir or root.startswith(active_dir + os.sep):
                        should_preserve_dir = True
                        logger.debug(f"Preserving compressed files in directory: {root} (active backup)")
                        preserved_compressed += sum(1 for f in files if f.endswith(('.zip', '.gz', '.bz2', '.xz')))
                        break

                if should_preserve_dir:
                    # Skip this directory and all its files
                    continue

                for file in files:
                    if file.endswith(('.zip', '.gz', '.bz2', '.xz')):
                        file_path = os.path.join(root, file)
                        try:
                            os.remove(file_path)
                            removed_compressed += 1
                            logger.debug(f"Removed compressed file: {file_path}")
                        except Exception as e:
                            logger.warning(f"Could not remove compressed file {file_path}: {str(e)}")

        logger.info(f"Removed {removed_compressed} compressed files during emergency cleanup")
        if preserved_compressed > 0:
            logger.info(f"Preserved {preserved_compressed} compressed files in active backup directories")

        # Check disk space again
        disk_percent, is_critical = self.check_disk_space()

        # If still critical, try more aggressive cleanup
        if is_critical:
            logger.warning("Disk space still critical, performing aggressive cleanup")
            return self.aggressive_cleanup()

        return not is_critical

    def aggressive_cleanup(self) -> bool:
        """
        Perform aggressive cleanup when emergency cleanup is not enough.
        This will remove all JSON files and other large files in the temp directory,
        while preserving active backup files.

        Returns:
            True if cleanup was successful, False otherwise
        """
        logger.warning("Performing aggressive cleanup to free up disk space")

        # Get active backup directories that should be preserved
        active_dirs = self.get_active_backup_directories()
        if active_dirs:
            logger.warning(f"Found {len(active_dirs)} active backup directories that will be preserved during aggressive cleanup")

        removed_files = 0
        removed_size = 0
        preserved_files = 0
        preserved_size = 0

        # Remove all JSON files (which can be very large)
        if os.path.exists(self.temp_dir):
            for root, _, files in os.walk(self.temp_dir):
                # Check if this directory should be preserved
                should_preserve_dir = False
                for active_dir in active_dirs:
                    if root == active_dir or root.startswith(active_dir + os.sep):
                        should_preserve_dir = True
                        break

                if should_preserve_dir:
                    # Count preserved files for logging
                    for file in files:
                        if file.endswith('.json') or file.endswith('.tmp') or file.endswith('.dat'):
                            try:
                                file_path = os.path.join(root, file)
                                file_size = os.path.getsize(file_path)
                                preserved_files += 1
                                preserved_size += file_size
                            except Exception:
                                pass
                    continue

                for file in files:
                    if file.endswith('.json') or file.endswith('.tmp') or file.endswith('.dat'):
                        file_path = os.path.join(root, file)
                        try:
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            removed_files += 1
                            removed_size += file_size
                            logger.debug(f"Removed file: {file_path} ({file_size / (1024*1024):.2f} MB)")
                        except Exception as e:
                            logger.warning(f"Could not remove file {file_path}: {str(e)}")

        logger.info(f"Aggressively removed {removed_files} files ({removed_size / (1024*1024):.2f} MB)")
        if preserved_files > 0:
            logger.info(f"Preserved {preserved_files} files ({preserved_size / (1024*1024):.2f} MB) in active backup directories")

        # Check disk space again
        disk_percent, is_critical = self.check_disk_space()

        # If still critical, try to remove large files
        if is_critical:
            logger.warning("Disk space still critical, removing large files")
            return self.remove_large_files()

        return not is_critical

    def remove_large_files(self, min_size_mb: int = 50) -> bool:
        """
        Remove large files to free up disk space, while preserving active backup files.

        Args:
            min_size_mb: Minimum file size in MB to consider for removal

        Returns:
            True if cleanup was successful, False otherwise
        """
        logger.warning(f"Removing files larger than {min_size_mb} MB to free up disk space")

        # Get active backup directories that should be preserved
        active_dirs = self.get_active_backup_directories()
        if active_dirs:
            logger.warning(f"Found {len(active_dirs)} active backup directories that will be preserved during large file cleanup")

        min_size_bytes = min_size_mb * 1024 * 1024
        removed_files = 0
        removed_size = 0
        preserved_large_files = 0
        preserved_large_size = 0

        # Find and remove large files in temp directory
        if os.path.exists(self.temp_dir):
            for root, _, files in os.walk(self.temp_dir):
                # Check if this directory should be preserved
                should_preserve_dir = False
                for active_dir in active_dirs:
                    if root == active_dir or root.startswith(active_dir + os.sep):
                        should_preserve_dir = True
                        break

                if should_preserve_dir:
                    # Count preserved large files for logging
                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            file_size = os.path.getsize(file_path)
                            if file_size >= min_size_bytes:
                                preserved_large_files += 1
                                preserved_large_size += file_size
                                logger.debug(f"Preserving large file: {file_path} ({file_size / (1024*1024):.2f} MB) - active backup")
                        except Exception:
                            pass
                    continue

                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        file_size = os.path.getsize(file_path)
                        if file_size >= min_size_bytes:
                            os.remove(file_path)
                            removed_files += 1
                            removed_size += file_size
                            logger.debug(f"Removed large file: {file_path} ({file_size / (1024*1024):.2f} MB)")
                    except Exception as e:
                        logger.warning(f"Could not check/remove file {file_path}: {str(e)}")

        logger.info(f"Removed {removed_files} large files ({removed_size / (1024*1024):.2f} MB)")
        if preserved_large_files > 0:
            logger.info(f"Preserved {preserved_large_files} large files ({preserved_large_size / (1024*1024):.2f} MB) in active backup directories")

        # Check disk space again
        disk_percent, is_critical = self.check_disk_space()
        return not is_critical

    def run_cleanup(self, force: bool = False) -> bool:
        """
        Run the cleanup process.

        Args:
            force: If True, force cleanup regardless of disk usage

        Returns:
            True if cleanup was successful, False otherwise
        """
        # Check disk space
        disk_percent, is_critical = self.check_disk_space()

        # Determine if cleanup is needed
        if not force and not is_critical:
            logger.info("Disk usage is below threshold, no cleanup needed")
            return True

        # Check if emergency cleanup is needed
        if disk_percent >= self.emergency_threshold:
            return self.emergency_cleanup()

        # Regular cleanup
        self.cleanup_temp_directory(force=force)
        self.cleanup_old_logs()

        # Check disk space after cleanup
        disk_percent, is_critical = self.check_disk_space()
        return not is_critical

def cleanup_temp_files(force: bool = False, age_hours: int = 3) -> int:
    """
    Convenience function to clean up temporary files.

    Args:
        force: If True, remove all files regardless of age
        age_hours: Remove files older than this many hours

    Returns:
        Number of files removed
    """
    cleanup = DiskCleanup()
    return cleanup.cleanup_temp_directory(force=force, age_hours=age_hours)

def setup_logging(verbose: bool = False):
    """Set up logging configuration."""
    log_level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

def main():
    """Main function when run as a script."""
    parser = argparse.ArgumentParser(description='Disk Cleanup Utility for Devo Backup System')
    parser.add_argument('--force', action='store_true', help='Force cleanup regardless of disk usage')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')
    parser.add_argument('--emergency', action='store_true', help='Perform emergency cleanup')
    args = parser.parse_args()

    # Set up logging
    setup_logging(args.verbose)

    # Run cleanup
    cleanup = DiskCleanup()
    if args.emergency:
        success = cleanup.emergency_cleanup()
    else:
        success = cleanup.run_cleanup(force=args.force)

    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
