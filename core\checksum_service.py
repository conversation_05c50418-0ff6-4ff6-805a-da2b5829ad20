#!/usr/bin/env python3
"""
Checksum Service Module

This module provides dedicated checksum and integrity verification functionality
extracted from StorageManager. It handles file integrity verification using
multiple hash algorithms with optimized performance.

Features:
- Multiple hash algorithms (SHA256, MD5, SHA1, SHA512)
- Batch checksum calculation
- File integrity verification
- Progress reporting for large files
- Memory-efficient streaming calculation
"""

import os
import logging
import hashlib
import time
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path

# Configure logging
logger = logging.getLogger(__name__)

class ChecksumService:
    """
    Dedicated service for file checksum calculation and integrity verification.
    
    This class handles all checksum-related functionality that was previously
    embedded in the StorageManager class, following the Single Responsibility Principle.
    """
    
    def __init__(self, config_manager=None):
        """
        Initialize the checksum service.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.checksum_stats = {
            'files_processed': 0,
            'bytes_processed': 0,
            'processing_time': 0.0,
            'verification_count': 0,
            'verification_failures': 0
        }
        
        # Supported hash algorithms
        self.supported_algorithms = {
            'sha256': hashlib.sha256,
            'md5': hashlib.md5,
            'sha1': hashlib.sha1,
            'sha512': hashlib.sha512
        }
        
    def calculate_file_checksum(self, file_path: str, algorithm: str = 'sha256') -> Optional[str]:
        """
        Calculate checksum for a single file.
        
        Args:
            file_path: Path to the file
            algorithm: Hash algorithm to use ('sha256', 'md5', 'sha1', 'sha512')
            
        Returns:
            Hexadecimal checksum string or None if error
        """
        start_time = time.time()
        
        try:
            # Validate algorithm
            if algorithm.lower() not in self.supported_algorithms:
                logger.error(f"Unsupported hash algorithm: {algorithm}")
                return None
            
            # Validate file
            if not os.path.exists(file_path):
                logger.error(f"File not found: {file_path}")
                return None
                
            if not os.path.isfile(file_path):
                logger.error(f"Path is not a file: {file_path}")
                return None
            
            # Get file size for progress reporting
            file_size = os.path.getsize(file_path)
            
            # Create hash object
            hash_obj = self.supported_algorithms[algorithm.lower()]()
            
            # Calculate checksum with progress reporting for large files
            bytes_read = 0
            chunk_size = 8192  # 8KB chunks
            
            with open(file_path, 'rb') as f:
                while chunk := f.read(chunk_size):
                    hash_obj.update(chunk)
                    bytes_read += len(chunk)
                    
                    # Progress reporting for files > 100MB
                    if file_size > 100 * 1024 * 1024 and bytes_read % (10 * 1024 * 1024) == 0:
                        progress = (bytes_read / file_size) * 100
                        logger.debug(f"Checksum progress for {os.path.basename(file_path)}: {progress:.1f}%")
            
            checksum = hash_obj.hexdigest()
            processing_time = time.time() - start_time
            
            # Update statistics
            self.checksum_stats['files_processed'] += 1
            self.checksum_stats['bytes_processed'] += file_size
            self.checksum_stats['processing_time'] += processing_time
            
            logger.debug(f"Calculated {algorithm.upper()} checksum for {file_path}: {checksum}")
            return checksum
            
        except Exception as e:
            logger.error(f"Error calculating {algorithm} checksum for {file_path}: {str(e)}")
            return None
    
    def calculate_directory_checksums(self, directory: str, algorithm: str = 'sha256') -> Dict[str, str]:
        """
        Calculate checksums for all files in a directory.
        
        Args:
            directory: Directory path
            algorithm: Hash algorithm to use
            
        Returns:
            Dictionary mapping relative file paths to checksums
        """
        checksums = {}
        
        try:
            if not os.path.exists(directory):
                logger.error(f"Directory not found: {directory}")
                return checksums
                
            if not os.path.isdir(directory):
                logger.error(f"Path is not a directory: {directory}")
                return checksums
            
            logger.info(f"Calculating {algorithm.upper()} checksums for directory: {directory}")
            
            file_count = 0
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, directory)
                    
                    checksum = self.calculate_file_checksum(file_path, algorithm)
                    if checksum:
                        checksums[relative_path] = checksum
                        file_count += 1
                        
                        # Progress reporting every 100 files
                        if file_count % 100 == 0:
                            logger.info(f"Calculated checksums for {file_count} files")
            
            logger.info(f"Completed checksum calculation for {file_count} files")
            return checksums
            
        except Exception as e:
            logger.error(f"Error calculating directory checksums: {str(e)}")
            return checksums
    
    def verify_file_integrity(self, file_path: str, expected_checksum: str, 
                            algorithm: str = 'sha256') -> bool:
        """
        Verify file integrity against expected checksum.
        
        Args:
            file_path: Path to the file
            expected_checksum: Expected checksum value
            algorithm: Hash algorithm used
            
        Returns:
            True if checksum matches, False otherwise
        """
        try:
            self.checksum_stats['verification_count'] += 1
            
            calculated_checksum = self.calculate_file_checksum(file_path, algorithm)
            if calculated_checksum is None:
                self.checksum_stats['verification_failures'] += 1
                return False
            
            matches = calculated_checksum.lower() == expected_checksum.lower()
            
            if matches:
                logger.debug(f"Integrity verification passed for {file_path}")
            else:
                logger.error(f"Integrity verification failed for {file_path}")
                logger.error(f"Expected: {expected_checksum}")
                logger.error(f"Calculated: {calculated_checksum}")
                self.checksum_stats['verification_failures'] += 1
            
            return matches
            
        except Exception as e:
            logger.error(f"Error verifying file integrity: {str(e)}")
            self.checksum_stats['verification_failures'] += 1
            return False
    
    def batch_verify_integrity(self, file_checksums: Dict[str, str], 
                             algorithm: str = 'sha256') -> Dict[str, bool]:
        """
        Verify integrity for multiple files.
        
        Args:
            file_checksums: Dictionary mapping file paths to expected checksums
            algorithm: Hash algorithm used
            
        Returns:
            Dictionary mapping file paths to verification results
        """
        results = {}
        
        try:
            logger.info(f"Starting batch integrity verification for {len(file_checksums)} files")
            
            verified_count = 0
            for file_path, expected_checksum in file_checksums.items():
                result = self.verify_file_integrity(file_path, expected_checksum, algorithm)
                results[file_path] = result
                
                if result:
                    verified_count += 1
                
                # Progress reporting every 50 files
                if len(results) % 50 == 0:
                    logger.info(f"Verified {len(results)}/{len(file_checksums)} files")
            
            success_rate = (verified_count / len(file_checksums)) * 100 if file_checksums else 0
            logger.info(f"Batch verification completed: {verified_count}/{len(file_checksums)} "
                       f"files verified successfully ({success_rate:.1f}%)")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in batch integrity verification: {str(e)}")
            return results
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        Get comprehensive file information including checksums.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary with file information and checksums
        """
        info = {
            'path': file_path,
            'exists': False,
            'size_bytes': 0,
            'checksums': {}
        }
        
        try:
            if not os.path.exists(file_path):
                return info
            
            info['exists'] = True
            info['size_bytes'] = os.path.getsize(file_path)
            
            # Calculate checksums for common algorithms
            for algorithm in ['sha256', 'md5']:
                checksum = self.calculate_file_checksum(file_path, algorithm)
                if checksum:
                    info['checksums'][algorithm] = checksum
            
            return info
            
        except Exception as e:
            logger.error(f"Error getting file info for {file_path}: {str(e)}")
            return info
    
    def get_checksum_stats(self) -> Dict[str, Any]:
        """
        Get current checksum calculation statistics.
        
        Returns:
            Dictionary with checksum statistics
        """
        stats = self.checksum_stats.copy()
        
        # Calculate additional metrics
        if stats['verification_count'] > 0:
            stats['verification_success_rate'] = (
                (stats['verification_count'] - stats['verification_failures']) / 
                stats['verification_count'] * 100
            )
        else:
            stats['verification_success_rate'] = 0.0
        
        if stats['processing_time'] > 0 and stats['bytes_processed'] > 0:
            stats['throughput_mbps'] = (
                stats['bytes_processed'] / (1024 * 1024) / stats['processing_time']
            )
        else:
            stats['throughput_mbps'] = 0.0
        
        return stats
    
    def reset_stats(self):
        """Reset checksum statistics."""
        self.checksum_stats = {
            'files_processed': 0,
            'bytes_processed': 0,
            'processing_time': 0.0,
            'verification_count': 0,
            'verification_failures': 0
        }
