#!/usr/bin/env python3
"""
Unit tests for the ConfigManager class.
"""

import os
import unittest
from unittest.mock import patch, mock_open
import json

from core.config_manager import ConfigManager


class TestConfigManager(unittest.TestCase):
    """Test cases for the ConfigManager class."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a test configuration
        self.test_config = {
            "backup": {
                "default_days": 2,
                "default_chunk_size": 1000,
                "default_max_retries": 3,
                "default_retry_delay": 2,
                "default_timeout": 300,
                "default_max_threads": 4
            },
            "storage": {
                "oss_path_template": "test/{table_name}_{date_str}.zip",
                "summary_file_template": "test_summary_{date_str}.json"
            }
        }

    @patch("builtins.open", new_callable=mock_open, read_data='{"backup": {"default_days": 2}}')
    @patch("os.path.exists", return_value=True)
    @patch("core.config_manager.load_dotenv")
    def test_load_config_file(self, mock_load_dotenv, mock_exists, mock_file):
        """Test loading configuration from a file."""
        # Ensure dotenv doesn't try to load any files
        mock_load_dotenv.return_value = False

        config_manager = ConfigManager()
        self.assertEqual(config_manager.config.get("backup", {}).get("default_days"), 2)

    @patch("os.path.exists", return_value=False)
    @patch("core.config_manager.load_dotenv")
    def test_load_config_file_not_found(self, mock_load_dotenv, mock_exists):
        """Test loading configuration when file not found."""
        # Ensure dotenv doesn't try to load any files
        mock_load_dotenv.return_value = False

        # Create a config manager with a non-existent file
        config_manager = ConfigManager(config_file="non_existent_file.json")

        # It should use the default config
        self.assertEqual(config_manager.config, ConfigManager.DEFAULT_CONFIG)

    @patch("builtins.open", new_callable=mock_open, read_data='{"backup": {"default_days": 2}}')
    @patch("os.path.exists", return_value=True)
    @patch("core.config_manager.load_dotenv")
    def test_get_with_type_validation_int(self, mock_load_dotenv, mock_exists, mock_file):
        """Test getting a value with type validation for int."""
        # Ensure dotenv doesn't try to load any files
        mock_load_dotenv.return_value = False

        config_manager = ConfigManager()
        value = config_manager.get("backup", "default_days", 1, value_type=int)
        self.assertEqual(value, 2)
        self.assertIsInstance(value, int)

    @patch("builtins.open", new_callable=mock_open, read_data='{"backup": {"flag": "true"}}')
    @patch("os.path.exists", return_value=True)
    @patch("core.config_manager.load_dotenv")
    def test_get_with_type_validation_bool(self, mock_load_dotenv, mock_exists, mock_file):
        """Test getting a value with type validation for bool."""
        # Ensure dotenv doesn't try to load any files
        mock_load_dotenv.return_value = False

        config_manager = ConfigManager()
        value = config_manager.get("backup", "flag", False, value_type=bool)
        self.assertEqual(value, True)
        self.assertIsInstance(value, bool)

    @patch("builtins.open", new_callable=mock_open, read_data='{"backup": {"invalid_int": "not_an_int"}}')
    @patch("os.path.exists", return_value=True)
    @patch("core.config_manager.load_dotenv")
    def test_get_with_type_validation_error(self, mock_load_dotenv, mock_exists, mock_file):
        """Test getting a value with type validation when conversion fails."""
        # Ensure dotenv doesn't try to load any files
        mock_load_dotenv.return_value = False

        config_manager = ConfigManager()
        value = config_manager.get("backup", "invalid_int", 1, value_type=int)
        self.assertEqual(value, 1)  # Should return the default value

    @patch("builtins.open", new_callable=mock_open, read_data='{"backup": {"default_days": 2}}')
    @patch("os.path.exists", return_value=True)
    @patch("core.config_manager.load_dotenv")
    def test_get_default_value(self, mock_load_dotenv, mock_exists, mock_file):
        """Test getting a value that doesn't exist."""
        # Ensure dotenv doesn't try to load any files
        mock_load_dotenv.return_value = False

        config_manager = ConfigManager()
        value = config_manager.get("backup", "non_existent_key", "default_value")
        self.assertEqual(value, "default_value")

    @patch.dict(os.environ, {"TEST_ENV_VAR": "test_value"})
    @patch("core.config_manager.load_dotenv")
    def test_get_env(self, mock_load_dotenv):
        """Test getting a value from environment variables."""
        # Ensure dotenv doesn't try to load any files
        mock_load_dotenv.return_value = False

        config_manager = ConfigManager()
        value = config_manager.get_env("TEST_ENV_VAR", "default")
        self.assertEqual(value, "test_value")

    @patch.dict(os.environ, {}, clear=True)
    @patch("core.config_manager.load_dotenv")
    def test_get_env_default(self, mock_load_dotenv):
        """Test getting a value from environment variables when not set."""
        # Ensure dotenv doesn't try to load any files
        mock_load_dotenv.return_value = False

        config_manager = ConfigManager()
        value = config_manager.get_env("NON_EXISTENT_VAR", "default")
        self.assertEqual(value, "default")


if __name__ == "__main__":
    unittest.main()
