#!/usr/bin/env python3
"""
Test Code Duplication Fixes

This module tests that code duplication has been properly eliminated
and that common utilities work correctly.
"""

import unittest
import os
import sys
from unittest.mock import patch, MagicMock

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from utils.common_imports import (
    setup_project_path,
    get_common_imports,
    ProjectPathManager,
    get_standard_config,
    get_standard_backup_config
)


class TestCodeDuplicationFixes(unittest.TestCase):
    """Test that code duplication has been properly eliminated."""

    def test_project_path_setup(self):
        """Test that project path setup works correctly."""
        # Test with a mock file path
        mock_file_path = os.path.join(project_root, 'scripts', 'test_script.py')
        
        # Setup project path
        result_path = setup_project_path(mock_file_path)
        
        # Verify the path is set correctly
        self.assertTrue(os.path.exists(result_path))
        self.assertIn(result_path, sys.path)

    def test_project_path_manager_context(self):
        """Test that ProjectPathManager context manager works."""
        original_path = sys.path.copy()
        
        with ProjectPathManager(__file__) as project_path:
            # Verify path is set up
            self.assertIsInstance(project_path, str)
            self.assertTrue(os.path.exists(project_path))
        
        # Verify path is restored (approximately - some changes may persist)
        # We check that no extra paths were permanently added
        self.assertLessEqual(len(sys.path), len(original_path) + 2)

    def test_common_imports_available(self):
        """Test that common imports can be retrieved."""
        imports = get_common_imports()
        
        # Should return a dictionary
        self.assertIsInstance(imports, dict)
        
        # May be empty if imports fail, but should not raise exception
        # This is acceptable as it provides graceful fallback

    def test_standard_config_creation(self):
        """Test that standard configuration can be created."""
        # This may return None if imports fail, which is acceptable
        config = get_standard_config()
        
        # Should either be a config object or None (graceful fallback)
        self.assertTrue(config is None or hasattr(config, 'get'))

    def test_standard_backup_config_creation(self):
        """Test that standard backup configuration can be created."""
        # This may return None if imports fail, which is acceptable
        backup_config = get_standard_backup_config()
        
        # Should either be a backup config object or None (graceful fallback)
        self.assertTrue(backup_config is None or hasattr(backup_config, 'config_manager'))

    def test_no_duplicate_test_files_in_core(self):
        """Test that test files have been moved out of core directory."""
        core_dir = os.path.join(project_root, 'core')
        
        if os.path.exists(core_dir):
            core_files = os.listdir(core_dir)
            
            # Check that no test files remain in core
            test_files = [f for f in core_files if f.endswith('_test.py') or f.startswith('test_')]
            
            self.assertEqual(len(test_files), 0, 
                           f"Test files found in core directory: {test_files}")

    def test_storage_manager_test_removed(self):
        """Test that the duplicate storage_manager_test.py has been removed."""
        duplicate_file = os.path.join(project_root, 'core', 'storage_manager_test.py')
        
        # This file should no longer exist
        self.assertFalse(os.path.exists(duplicate_file),
                        "Duplicate storage_manager_test.py still exists in core directory")

    def test_unified_table_processor_test_moved(self):
        """Test that unified_table_processor_test.py has been moved to tests."""
        old_location = os.path.join(project_root, 'core', 'unified_table_processor_test.py')
        new_location = os.path.join(project_root, 'tests', 'core', 'test_unified_table_processor.py')
        
        # Old location should not exist
        self.assertFalse(os.path.exists(old_location),
                        "unified_table_processor_test.py still exists in core directory")
        
        # New location should exist
        self.assertTrue(os.path.exists(new_location),
                       "test_unified_table_processor.py not found in tests/core directory")

    def test_moved_test_file_imports_correctly(self):
        """Test that the moved test file can import correctly."""
        try:
            # Try to import the moved test file
            from tests.core.test_unified_table_processor import TestSmartProcessingStrategy
            
            # If we can import it, the path setup worked
            self.assertTrue(hasattr(TestSmartProcessingStrategy, 'setUp'))
            
        except ImportError as e:
            # If import fails, it might be due to missing dependencies
            # This is acceptable as long as the file exists and has correct structure
            test_file = os.path.join(project_root, 'tests', 'core', 'test_unified_table_processor.py')
            self.assertTrue(os.path.exists(test_file),
                           f"Test file should exist even if imports fail: {e}")


class TestCodeOrganization(unittest.TestCase):
    """Test that code organization improvements are working."""

    def test_tests_directory_structure(self):
        """Test that tests directory has proper structure."""
        tests_dir = os.path.join(project_root, 'tests')
        
        # Tests directory should exist
        self.assertTrue(os.path.exists(tests_dir))
        
        # Core tests subdirectory should exist
        core_tests_dir = os.path.join(tests_dir, 'core')
        self.assertTrue(os.path.exists(core_tests_dir))

    def test_no_production_code_in_tests(self):
        """Test that tests directory doesn't contain production code."""
        tests_dir = os.path.join(project_root, 'tests')
        
        if os.path.exists(tests_dir):
            for root, dirs, files in os.walk(tests_dir):
                for file in files:
                    if file.endswith('.py') and not file.startswith('test_') and file != '__init__.py':
                        # Allow some utility files but flag potential production code
                        if not any(keyword in file.lower() for keyword in ['util', 'helper', 'common']):
                            self.fail(f"Potential production code found in tests: {os.path.join(root, file)}")

    def test_common_utilities_consolidation(self):
        """Test that common utilities are properly consolidated."""
        common_imports_file = os.path.join(project_root, 'utils', 'common_imports.py')
        
        # Common imports utility should exist
        self.assertTrue(os.path.exists(common_imports_file))
        
        # Should contain the expected functions
        with open(common_imports_file, 'r') as f:
            content = f.read()
            
        expected_functions = [
            'setup_project_path',
            'get_common_imports',
            'ProjectPathManager',
            'get_standard_config'
        ]
        
        for func in expected_functions:
            self.assertIn(func, content, f"Function {func} not found in common_imports.py")


if __name__ == '__main__':
    unittest.main(verbosity=2)
