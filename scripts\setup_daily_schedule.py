#!/usr/bin/env python3
"""
Windows Task Scheduler Integration for Daily Backups

This script sets up automated daily backup tasks using Windows Task Scheduler.
It provides a programmatic way to create, modify, and manage scheduled backup tasks.

Features:
- Create daily backup tasks with customizable schedules
- Configure task settings for reliability and security
- Set up multiple backup strategies (daily, weekly, monthly)
- Monitor and manage existing backup tasks
- Automatic error handling and logging
- Integration with existing backup system

Usage:
    python scripts/setup_daily_schedule.py --create --time "01:00"
    python scripts/setup_daily_schedule.py --list
    python scripts/setup_daily_schedule.py --delete --task-name "DailyBackup"
    python scripts/setup_daily_schedule.py --status --task-name "DailyBackup"
"""

import os
import sys
import argparse
import logging
import subprocess
import datetime
import json
from typing import Dict, Any, List, Optional
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import project modules
from core.config_manager import ConfigManager
from utils.minimal_logging import logger

class WindowsTaskScheduler:
    """
    Windows Task Scheduler integration for automated backup scheduling.
    """

    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the Windows Task Scheduler integration.

        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager or ConfigManager()
        self.project_root = project_root
        self.backup_script = self.project_root / "scripts" / "daily_backup_scheduler.py"

    def create_daily_backup_task(self, task_name: str = "TNGD_DailyBackup",
                                schedule_time: str = "01:00",
                                description: str = "TNGD Daily Database Backup") -> bool:
        """
        Create a daily backup task in Windows Task Scheduler.

        Args:
            task_name: Name of the task
            schedule_time: Time to run the task (HH:MM format)
            description: Task description

        Returns:
            True if task was created successfully, False otherwise
        """
        logger.log_operation("CREATING DAILY BACKUP TASK", "STARTED", 
                           f"Task: {task_name}, Schedule: {schedule_time}")

        try:
            # Validate schedule time format
            if not self._validate_time_format(schedule_time):
                logger.error(f"Invalid time format: {schedule_time}. Use HH:MM format.")
                return False

            # Check if task already exists
            if self._task_exists(task_name):
                logger.warning(f"Task {task_name} already exists. Use --force to overwrite.")
                return False

            # Prepare task command
            python_exe = sys.executable
            script_path = str(self.backup_script)

            # Build the command that will be executed by the task
            task_command = f'"{python_exe}" "{script_path}"'

            # Create the task using schtasks command
            schtasks_cmd = [
                "schtasks",
                "/create",
                "/tn", task_name,
                "/tr", task_command,
                "/sc", "daily",
                "/st", schedule_time,
                "/ru", "SYSTEM",
                "/rl", "HIGHEST",
                "/f"  # Force creation (overwrite if exists)
            ]

            # Add description if provided
            if description:
                schtasks_cmd.extend(["/desc", description])

            logger.info(f"Executing: {' '.join(schtasks_cmd)}")

            # Execute the schtasks command
            result = subprocess.run(
                schtasks_cmd,
                capture_output=True,
                text=True,
                check=False
            )

            if result.returncode == 0:
                logger.info(f"Task {task_name} created successfully")
                logger.info(f"Task will run daily at {schedule_time}")

                # Configure additional task settings
                self._configure_task_settings(task_name)

                return True
            else:
                logger.error(f"Failed to create task. Error: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error creating daily backup task: {str(e)}")
            return False

    def create_weekly_backup_task(self, task_name: str = "TNGD_WeeklyBackup",
                                 schedule_time: str = "02:00",
                                 day_of_week: str = "SUN") -> bool:
        """
        Create a weekly backup task in Windows Task Scheduler.

        Args:
            task_name: Name of the task
            schedule_time: Time to run the task (HH:MM format)
            day_of_week: Day of the week (MON, TUE, WED, THU, FRI, SAT, SUN)

        Returns:
            True if task was created successfully, False otherwise
        """
        logger.info(f"Creating weekly backup task: {task_name}")

        try:
            # Prepare command for weekly backup
            python_exe = sys.executable
            script_path = str(self.backup_script)
            task_command = f'"{python_exe}" "{script_path}" --weekly'

            # Create the weekly task
            schtasks_cmd = [
                "schtasks",
                "/create",
                "/tn", task_name,
                "/tr", task_command,
                "/sc", "weekly",
                "/d", day_of_week,
                "/st", schedule_time,
                "/ru", "SYSTEM",
                "/rl", "HIGHEST",
                "/f"
            ]

            result = subprocess.run(
                schtasks_cmd,
                capture_output=True,
                text=True,
                check=False
            )

            if result.returncode == 0:
                logger.info(f"Weekly task {task_name} created successfully")
                return True
            else:
                logger.error(f"Failed to create weekly task. Error: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error creating weekly backup task: {str(e)}")
            return False

    def create_monthly_backup_task(self, task_name: str = "TNGD_MonthlyBackup",
                                  schedule_time: str = "03:00",
                                  day_of_month: int = 1) -> bool:
        """
        Create a monthly backup task in Windows Task Scheduler.

        Args:
            task_name: Name of the task
            schedule_time: Time to run the task (HH:MM format)
            day_of_month: Day of the month (1-31)

        Returns:
            True if task was created successfully, False otherwise
        """
        logger.info(f"Creating monthly backup task: {task_name}")

        try:
            # Prepare command for monthly backup
            python_exe = sys.executable
            script_path = str(self.backup_script)
            task_command = f'"{python_exe}" "{script_path}" --monthly'

            # Create the monthly task
            schtasks_cmd = [
                "schtasks",
                "/create",
                "/tn", task_name,
                "/tr", task_command,
                "/sc", "monthly",
                "/d", str(day_of_month),
                "/st", schedule_time,
                "/ru", "SYSTEM",
                "/rl", "HIGHEST",
                "/f"
            ]

            result = subprocess.run(
                schtasks_cmd,
                capture_output=True,
                text=True,
                check=False
            )

            if result.returncode == 0:
                logger.info(f"Monthly task {task_name} created successfully")
                return True
            else:
                logger.error(f"Failed to create monthly task. Error: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error creating monthly backup task: {str(e)}")
            return False

    def list_backup_tasks(self) -> List[Dict[str, Any]]:
        """
        List all TNGD backup tasks in Task Scheduler.

        Returns:
            List of task information dictionaries
        """
        logger.info("Listing TNGD backup tasks")

        try:
            # Query tasks with TNGD prefix
            result = subprocess.run(
                ["schtasks", "/query", "/fo", "csv", "/v"],
                capture_output=True,
                text=True,
                check=False
            )

            if result.returncode != 0:
                logger.error(f"Failed to query tasks: {result.stderr}")
                return []

            # Parse CSV output and filter TNGD tasks
            lines = result.stdout.strip().split('\n')
            if len(lines) < 2:
                return []

            # Parse header
            headers = [h.strip('"') for h in lines[0].split('","')]
            tasks = []

            for line in lines[1:]:
                if 'TNGD' in line:
                    # Parse task data
                    values = [v.strip('"') for v in line.split('","')]
                    if len(values) >= len(headers):
                        task_data = dict(zip(headers, values))
                        tasks.append(task_data)

            logger.info(f"Found {len(tasks)} TNGD backup tasks")
            return tasks

        except Exception as e:
            logger.error(f"Error listing backup tasks: {str(e)}")
            return []

    def delete_task(self, task_name: str) -> bool:
        """
        Delete a backup task from Task Scheduler.

        Args:
            task_name: Name of the task to delete

        Returns:
            True if task was deleted successfully, False otherwise
        """
        logger.info(f"Deleting task: {task_name}")

        try:
            result = subprocess.run(
                ["schtasks", "/delete", "/tn", task_name, "/f"],
                capture_output=True,
                text=True,
                check=False
            )

            if result.returncode == 0:
                logger.info(f"Task {task_name} deleted successfully")
                return True
            else:
                logger.error(f"Failed to delete task. Error: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error deleting task {task_name}: {str(e)}")
            return False

    def get_task_status(self, task_name: str) -> Dict[str, Any]:
        """
        Get the status of a specific backup task.

        Args:
            task_name: Name of the task

        Returns:
            Task status information
        """
        logger.info(f"Getting status for task: {task_name}")

        try:
            result = subprocess.run(
                ["schtasks", "/query", "/tn", task_name, "/fo", "list", "/v"],
                capture_output=True,
                text=True,
                check=False
            )

            if result.returncode != 0:
                return {
                    'exists': False,
                    'error': result.stderr
                }

            # Parse the output
            status_info = {'exists': True}
            lines = result.stdout.strip().split('\n')

            for line in lines:
                if ':' in line:
                    key, value = line.split(':', 1)
                    key = key.strip()
                    value = value.strip()

                    if key == "Status":
                        status_info['status'] = value
                    elif key == "Last Run Time":
                        status_info['last_run'] = value
                    elif key == "Next Run Time":
                        status_info['next_run'] = value
                    elif key == "Last Result":
                        status_info['last_result'] = value

            return status_info

        except Exception as e:
            logger.error(f"Error getting task status: {str(e)}")
            return {
                'exists': False,
                'error': str(e)
            }

    def _validate_time_format(self, time_str: str) -> bool:
        """Validate time format (HH:MM)."""
        try:
            datetime.datetime.strptime(time_str, "%H:%M")
            return True
        except ValueError:
            return False

    def _task_exists(self, task_name: str) -> bool:
        """Check if a task already exists."""
        try:
            result = subprocess.run(
                ["schtasks", "/query", "/tn", task_name],
                capture_output=True,
                text=True,
                check=False
            )
            return result.returncode == 0
        except Exception:
            return False

    def _configure_task_settings(self, task_name: str):
        """Configure additional task settings for reliability."""
        try:
            # Set task to restart on failure
            subprocess.run([
                "schtasks", "/change", "/tn", task_name,
                "/it"  # Run only if user is logged on
            ], capture_output=True, check=False)

            logger.info(f"Additional settings configured for task {task_name}")

        except Exception as e:
            logger.warning(f"Could not configure additional settings: {str(e)}")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Windows Task Scheduler Integration for TNGD Backups',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    # Action selection
    action_group = parser.add_mutually_exclusive_group(required=True)
    action_group.add_argument('--create', action='store_true',
                             help='Create a new backup task')
    action_group.add_argument('--list', action='store_true',
                             help='List all TNGD backup tasks')
    action_group.add_argument('--delete', action='store_true',
                             help='Delete a backup task')
    action_group.add_argument('--status', action='store_true',
                             help='Get status of a backup task')
    action_group.add_argument('--create-all', action='store_true',
                             help='Create all backup tasks (daily, weekly, monthly)')

    # Task configuration
    parser.add_argument('--task-name', type=str, default='TNGD_DailyBackup',
                       help='Name of the task (default: TNGD_DailyBackup)')
    parser.add_argument('--time', type=str, default='01:00',
                       help='Schedule time in HH:MM format (default: 01:00)')
    parser.add_argument('--day-of-week', type=str, default='SUN',
                       choices=['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'],
                       help='Day of week for weekly tasks (default: SUN)')
    parser.add_argument('--day-of-month', type=int, default=1, choices=range(1, 32),
                       help='Day of month for monthly tasks (default: 1)')
    parser.add_argument('--force', action='store_true',
                       help='Force creation (overwrite existing tasks)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')

    return parser.parse_args()

def main():
    """Main function for task scheduler setup."""
    args = parse_arguments()

    try:
        # Log startup
        logger.log_operation("WINDOWS TASK SCHEDULER SETUP", "STARTED",
                           f"Started at: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        scheduler = WindowsTaskScheduler()

        if args.create:
            success = scheduler.create_daily_backup_task(
                task_name=args.task_name,
                schedule_time=args.time
            )
            return 0 if success else 1

        elif args.create_all:
            logger.info("Creating all backup tasks (daily, weekly, monthly)")

            # Create daily task
            daily_success = scheduler.create_daily_backup_task(
                task_name="TNGD_DailyBackup",
                schedule_time="01:00"
            )

            # Create weekly task
            weekly_success = scheduler.create_weekly_backup_task(
                task_name="TNGD_WeeklyBackup",
                schedule_time="02:00",
                day_of_week="SUN"
            )

            # Create monthly task
            monthly_success = scheduler.create_monthly_backup_task(
                task_name="TNGD_MonthlyBackup",
                schedule_time="03:00",
                day_of_month=1
            )

            if daily_success and weekly_success and monthly_success:
                logger.info("All backup tasks created successfully")
                return 0
            else:
                logger.error("Some tasks failed to create")
                return 1

        elif args.list:
            tasks = scheduler.list_backup_tasks()

            if tasks:
                print("\nTNGD Backup Tasks:")
                print("-" * 50)
                for task in tasks:
                    task_name = task.get('TaskName', 'Unknown')
                    status = task.get('Status', 'Unknown')
                    next_run = task.get('Next Run Time', 'Unknown')
                    print(f"Task: {task_name}")
                    print(f"Status: {status}")
                    print(f"Next Run: {next_run}")
                    print("-" * 30)
            else:
                print("No TNGD backup tasks found.")

            return 0

        elif args.delete:
            success = scheduler.delete_task(args.task_name)
            return 0 if success else 1

        elif args.status:
            status = scheduler.get_task_status(args.task_name)

            if status.get('exists'):
                print(f"\nTask Status for {args.task_name}:")
                print("-" * 40)
                for key, value in status.items():
                    if key != 'exists':
                        print(f"{key.replace('_', ' ').title()}: {value}")
            else:
                print(f"Task {args.task_name} does not exist.")
                if 'error' in status:
                    print(f"Error: {status['error']}")

            return 0

    except KeyboardInterrupt:
        logger.warning("Operation interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
