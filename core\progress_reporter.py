#!/usr/bin/env python3
"""
Progress Reporter Module

This module provides a unified way to report progress for long-running operations.
It supports both console output with tqdm progress bars and logging.
"""

import sys
import logging
import time
from typing import Dict, Any, Optional
from threading import Lock

# Import tqdm for progress bars if available
try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False

# Configure logging
logger = logging.getLogger(__name__)

class ProgressReporter:
    """
    Progress reporter for a single operation.
    Provides methods to update progress and status messages.
    """

    def __init__(self, name: str, desc: str = None, total: int = 100, use_tqdm: bool = True):
        """
        Initialize a progress reporter.

        Args:
            name: Unique name for this reporter
            desc: Description of the operation
            total: Total number of steps
            use_tqdm: Whether to use tqdm progress bars
        """
        self.name = name
        self.desc = desc or name
        self.total = total
        self.current = 0
        self.status = "Initializing..."
        self.start_time = time.time()
        self.last_update_time = self.start_time
        self.use_tqdm = use_tqdm and TQDM_AVAILABLE
        self.tqdm_bar = None

        # Initialize tqdm progress bar if requested
        if self.use_tqdm:
            self.tqdm_bar = tqdm(
                total=total,
                desc=desc,
                unit="tables",
                file=sys.stdout,
                leave=True
            )

        logger.info(f"Progress reporter '{name}' initialized: {desc} (total: {total})")

    def update(self, increment: int = 1, status: str = None) -> None:
        """
        Update the progress.

        Args:
            increment: Number of steps to increment
            status: New status message (optional)
        """
        # Update current position
        self.current += increment

        # Update status if provided
        if status:
            self.status = status

        # Update last update time
        self.last_update_time = time.time()

        # Update tqdm progress bar if available
        if self.tqdm_bar:
            self.tqdm_bar.update(increment)
            if status:
                self.tqdm_bar.set_description(f"{self.desc}: {status}")

        # Log the update with reduced frequency
        if status:
            # Only log significant progress (start, completion, or every 10% increment)
            if self.current == self.total or self.current == increment or self.current % max(1, int(self.total * 0.1)) == 0:
                logger.info(f"Progress '{self.name}': {self.current}/{self.total} - {status}")
            else:
                logger.debug(f"Progress '{self.name}': {self.current}/{self.total} - {status}")
        else:
            # Only log at debug level
            logger.debug(f"Progress '{self.name}': {self.current}/{self.total}")

    def set_total(self, total: int) -> None:
        """
        Set a new total for the progress.

        Args:
            total: New total number of steps
        """
        self.total = total

        # Update tqdm progress bar if available
        if self.tqdm_bar:
            self.tqdm_bar.total = total
            self.tqdm_bar.refresh()

        logger.info(f"Progress '{self.name}' total updated to {total}")

    def set_current(self, current: int, status: str = None) -> None:
        """
        Set the current progress value.

        Args:
            current: New current value
            status: New status message (optional)
        """
        # Calculate the increment
        increment = current - self.current

        # Update using the increment
        self.update(increment, status)

    def close(self) -> None:
        """Close the progress reporter and clean up resources."""
        # Close tqdm progress bar if available
        if self.tqdm_bar:
            self.tqdm_bar.close()
            self.tqdm_bar = None

        # Calculate duration
        duration = time.time() - self.start_time

        logger.info(f"Progress '{self.name}' completed: {self.current}/{self.total} in {duration:.2f} seconds")

    def get_progress(self) -> Dict[str, Any]:
        """
        Get the current progress information.

        Returns:
            Dictionary with progress information
        """
        current_time = time.time()
        duration = current_time - self.start_time

        return {
            "name": self.name,
            "description": self.desc,
            "current": self.current,
            "total": self.total,
            "percent": (self.current / self.total * 100) if self.total > 0 else 0,
            "status": self.status,
            "start_time": self.start_time,
            "last_update_time": self.last_update_time,
            "duration": duration,
            "active": (current_time - self.last_update_time) < 10  # Consider active if updated in last 10 seconds
        }


class ProgressManager:
    """
    Manager for multiple progress reporters.
    Provides methods to create, update, and retrieve progress reporters.
    """

    def __init__(self):
        """Initialize the progress manager."""
        self.reporters = {}
        self.lock = Lock()
        logger.debug("Progress manager initialized")

    def add_reporter(self, name: str, desc: str = None, total: int = 100, use_tqdm: bool = True) -> ProgressReporter:
        """
        Add a new progress reporter.

        Args:
            name: Unique name for the reporter
            desc: Description of the operation
            total: Total number of steps
            use_tqdm: Whether to use tqdm progress bars

        Returns:
            The created progress reporter
        """
        with self.lock:
            # Close existing reporter with the same name if it exists
            if name in self.reporters:
                self.reporters[name].close()

            # Create a new reporter
            reporter = ProgressReporter(name, desc, total, use_tqdm)
            self.reporters[name] = reporter

            return reporter

    def get_reporter(self, name: str) -> Optional[ProgressReporter]:
        """
        Get a progress reporter by name.

        Args:
            name: Name of the reporter to get

        Returns:
            The progress reporter or None if not found
        """
        with self.lock:
            return self.reporters.get(name)

    def update_progress(self, name: str, increment: int = 1, status: str = None) -> None:
        """
        Update the progress of a reporter.

        Args:
            name: Name of the reporter to update
            increment: Number of steps to increment
            status: New status message (optional)
        """
        with self.lock:
            reporter = self.reporters.get(name)
            if reporter:
                reporter.update(increment, status)
            else:
                logger.warning(f"Progress reporter '{name}' not found")

    def get_all_progress(self) -> Dict[str, Dict[str, Any]]:
        """
        Get the progress information for all reporters.

        Returns:
            Dictionary with progress information for all reporters
        """
        with self.lock:
            return {name: reporter.get_progress() for name, reporter in self.reporters.items()}

    def close_all(self) -> None:
        """Close all progress reporters."""
        with self.lock:
            for reporter in self.reporters.values():
                reporter.close()
            self.reporters = {}
            logger.debug("All progress reporters closed")

    def close_reporter(self, name: str) -> None:
        """
        Close a specific progress reporter.

        Args:
            name: Name of the reporter to close
        """
        with self.lock:
            reporter = self.reporters.pop(name, None)
            if reporter:
                reporter.close()
                logger.debug(f"Progress reporter '{name}' closed")
            else:
                logger.warning(f"Progress reporter '{name}' not found")
